{
    "configurations": [
        {
            "name": "Win32",
            "includePath": [
                "${workspaceFolder}/**",
                "A:/OpenCV-MinGW-Build-OpenCV-4.5.5-x64/include"
            ],
            "defines": [
                "_DEBUG",
                "UNICODE",
                "_UNICODE"
            ],
            "windowsSdkVersion": "10.0.19041.0", // Adjust if you have a different SDK
            "compilerPath": "C:/msys64/mingw64/bin/g++.exe", // PLEASE VERIFY this is your MinGW g++ compiler path
            "cStandard": "c17",
            "cppStandard": "c++17",
            "intelliSenseMode": "windows-gcc-x64" // Changed to gcc for MinGW
        }
    ],
    "version": 4
}
