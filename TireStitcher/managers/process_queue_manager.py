"""
Process queue manager for the Tire Panorama Tool.

Manages a queue of panorama generation processes.
"""
import time
import threading
import queue
from typing import Dict, List, Any, Optional, Callable, Tuple
from enum import Enum, auto


class TaskState(Enum):
    """Enumeration of possible task states"""
    QUEUED = auto()
    RUNNING = auto()
    COMPLETED = auto()
    FAILED = auto()
    CANCELLED = auto()


class ProcessQueueManager:
    """
    Manages a queue of panorama generation processes.

    This class handles the queuing and execution of panorama generation
    processes, allowing multiple tasks to be queued and executed sequentially.
    """

    def __init__(self):
        """Initialize the process queue manager."""
        self.process_queue = queue.Queue()
        self.tasks = []  # List to track all tasks for UI display

        # Use a reentrant lock for thread safety
        self.lock = threading.RLock()

        # State tracking
        self.current_task_id = None
        self.status_changed_callbacks = []

        # Add a flag to track if we're currently processing a status update
        # This helps prevent cascading updates
        self.is_updating = False

        # Add a timer to throttle UI updates
        self.last_update_time = 0

    def add_task(self, project_info: Dict[str, Any],
                 subproject_type: str, input_info: Dict[str, Any],
                 params: Dict[str, Any], continue_from_state: Optional[Dict[str, Any]] = None) -> int:
        """
        Add a new panorama generation task to the queue.

        Args:
            project_info: Dictionary with project name and directory
            subproject_type: Type of subproject ("frontal", "left", or "right")
            input_info: Dictionary with input path and type
            params: Dictionary with processing parameters
            continue_from_state: Optional state to continue from

        Returns:
            Task ID for the queued task
        """
        with self.lock:
            # Generate a task ID
            task_id = int(time.time() * 1000)

            # Create task dictionary
            task = {
                "id": task_id,
                "project_info": project_info,
                "subproject_type": subproject_type,
                "input_info": input_info,
                "params": params,
                "continue_from_state": continue_from_state,
                "status": TaskState.QUEUED,
                "progress": 0,
                "added_time": time.time(),
                "start_time": None,
                "completion_time": None,
                "error": None
            }

            # Add to tracking list
            self.tasks.append(task)

            # Add to queue
            self.process_queue.put(task_id)

            # Notify status change
            self._notify_status_changed()

            return task_id

    def cancel_task(self, task_id: int) -> Tuple[bool, str]:
        """
        Cancel a queued or running task.

        Args:
            task_id: ID of the task to cancel

        Returns:
            Tuple of (success, status_message) where:
            - success: True if task was cancelled or is already cancelled
            - status_message: Message explaining the result
        """
        with self.lock:
            # Find the task in our tracking list
            task = self._get_task_by_id(task_id)
            if not task:
                return False, "Task not found"

            # Get status string for comparison
            status = task["status"]
            status_str = status.name if hasattr(status, "name") else str(status)

            # Handle different task states
            if status_str == "QUEUED":
                # For queued tasks, we can remove from queue
                task["status"] = TaskState.CANCELLED
                task["completion_time"] = time.time()
                task["error"] = "Cancelled by user"

                # Rebuild the queue to remove this task
                self._rebuild_queue()

                # Notify status change
                self._notify_status_changed()
                return True, "Task removed from queue"

            elif status_str == "RUNNING":
                # For running tasks, indicate it's been requested to cancel
                # The actual cancellation needs to happen in the app
                task["error"] = "Cancellation requested"

                # Notify status change
                self._notify_status_changed()
                return True, "Cancellation requested for running task"

            elif status_str == "CANCELLED":
                # Already cancelled
                return True, "Task is already cancelled"

            else:
                # Other states (completed, failed) can't be cancelled
                return False, f"Cannot cancel task in state: {status_str}"

    def get_all_tasks(self) -> List[Dict[str, Any]]:
        """
        Get all tasks, including completed ones.

        Returns:
            List of task dictionaries
        """
        with self.lock:
            # Return a deep copy to avoid threading issues
            return [task.copy() for task in self.tasks]

    def get_active_tasks(self) -> List[Dict[str, Any]]:
        """
        Get all active (queued or running) tasks.

        Returns:
            List of active task dictionaries
        """
        with self.lock:
            return [task.copy() for task in self.tasks
                    if task["status"] in [TaskState.QUEUED, TaskState.RUNNING]]

    def get_task(self, task_id: int) -> Optional[Dict[str, Any]]:
        """
        Get a specific task by ID.

        Args:
            task_id: ID of the task to get

        Returns:
            Task dictionary or None if not found
        """
        with self.lock:
            task = self._get_task_by_id(task_id)
            if task:
                return task.copy()  # Return a copy to avoid threading issues
            return None

    def update_task_progress(self, task_id: int, progress: float) -> bool:
        """
        Update the progress of a task.

        Args:
            task_id: ID of the task to update
            progress: Progress value (0-100)

        Returns:
            True if task was updated, False otherwise
        """
        with self.lock:
            task = self._get_task_by_id(task_id)
            if not task:
                return False

            # Check if progress has changed significantly to avoid excessive updates
            current_progress = task.get("progress", 0)
            # Only update for significant changes (2% or more) or at start/end points
            # Reduced threshold for more responsive progress updates
            if (abs(current_progress - progress) < 2 and
                progress != 0 and progress < 100 and
                current_progress != 0):
                # Skip small progress changes to reduce UI updates
                return True

            task["progress"] = progress

            # Only notify status change for significant progress changes
            # This reduces UI flickering while allowing more frequent updates
            if progress == 0 or progress == 100 or abs(current_progress - progress) >= 2:
                self._notify_status_changed()
            return True

    def clear_completed_tasks(self, keep_count: int = 10) -> int:
        """
        Clear completed tasks, keeping a certain number of recent ones.

        Args:
            keep_count: Number of completed tasks to keep

        Returns:
            Number of tasks cleared
        """
        with self.lock:
            # Get completed tasks
            completed = [task for task in self.tasks
                         if task["status"] in [TaskState.COMPLETED, TaskState.FAILED, TaskState.CANCELLED]]

            # Sort by completion time (newest first)
            completed.sort(key=lambda t: t["completion_time"] or 0, reverse=True)

            # Keep the most recent ones
            to_keep = completed[:keep_count]
            to_keep_ids = {t["id"] for t in to_keep}

            # Filter tasks list
            new_tasks = [task for task in self.tasks
                        if task["status"] not in [TaskState.COMPLETED, TaskState.FAILED, TaskState.CANCELLED]
                        or task["id"] in to_keep_ids]

            # Calculate number cleared
            cleared_count = len(self.tasks) - len(new_tasks)

            # Update tasks list
            self.tasks = new_tasks

            # Notify status change
            self._notify_status_changed()

            return cleared_count

    def register_status_changed_callback(self, callback: Callable[[], None]) -> None:
        """
        Register a callback to be called when the queue status changes.

        Args:
            callback: Function to call
        """
        with self.lock:
            if callback not in self.status_changed_callbacks:
                self.status_changed_callbacks.append(callback)

    def set_task_running(self, task_id: int) -> bool:
        """
        Mark a task as running.

        Args:
            task_id: ID of the task to update

        Returns:
            True if task was updated, False otherwise
        """
        with self.lock:
            task = self._get_task_by_id(task_id)
            if not task:
                return False

            if task["status"] != TaskState.QUEUED:
                return False

            task["status"] = TaskState.RUNNING
            task["start_time"] = time.time()
            self.current_task_id = task_id

            # Notify status change
            self._notify_status_changed()
            return True

    def set_task_completed(self, task_id: int, success: bool, error: Optional[str] = None) -> bool:
        """
        Mark a task as completed.

        Args:
            task_id: ID of the task to update
            success: Whether the task completed successfully
            error: Optional error message if task failed

        Returns:
            True if task was updated, False otherwise
        """
        with self.lock:
            task = self._get_task_by_id(task_id)
            if not task:
                return False

            task["status"] = TaskState.COMPLETED if success else TaskState.FAILED
            task["completion_time"] = time.time()

            if not success and error:
                task["error"] = error

            # Reset current task ID if this was it
            if self.current_task_id == task_id:
                self.current_task_id = None

            # Notify status change
            self._notify_status_changed()
            return True

    def get_next_task_for_processing(self) -> Optional[Dict[str, Any]]:
        """
        Get the next task for processing (used by app to start processing).

        This method is designed to be called when a task completes, to
        find the next task in the queue that should start.

        Returns:
            Next task to process or None if no tasks are ready
        """
        with self.lock:
            # Check if a task is already running
            if self.current_task_id is not None:
                # Make sure the task actually exists and is in running state
                task = self._get_task_by_id(self.current_task_id)
                if task and task["status"] == TaskState.RUNNING:
                    return None  # Already running a task
                # If the task doesn't exist or isn't running, reset current task ID
                self.current_task_id = None

            # Try to get the next task ID from the queue
            try:
                # Peek at the queue using a non-blocking get
                if self.process_queue.empty():
                    return None  # No tasks in queue

                # Get the next task ID from the queue (without removing it)
                # We'll do a real get when the task starts running
                next_task_id = self._peek_queue()
                if next_task_id is None:
                    return None  # No more tasks

                # Find the task in our task list
                task = self._get_task_by_id(next_task_id)
                if not task or task["status"] != TaskState.QUEUED:
                    # Task not found or not queued, remove from queue and try again
                    try:
                        self.process_queue.get_nowait()  # Remove invalid task
                    except queue.Empty:
                        pass
                    return None

                # Return a copy of the task for the caller
                return task.copy()

            except Exception as e:
                print(f"Error getting next task: {e}")
                return None

    def dequeue_and_start_task(self, task_id: int) -> bool:
        """
        Dequeue a task and mark it as running.

        Args:
            task_id: ID of the task to dequeue and start

        Returns:
            True if task was dequeued and started, False otherwise
        """
        with self.lock:
            try:
                # Check queue again
                if self.process_queue.empty():
                    return False

                # Get the next task ID from the queue
                next_task_id = self.process_queue.get_nowait()

                # Verify it's the expected task
                if next_task_id != task_id:
                    # Something's wrong, put it back
                    self.process_queue.put(next_task_id)
                    return False

                # Mark the task as running
                return self.set_task_running(task_id)

            except Exception as e:
                print(f"Error dequeuing task: {e}")
                return False

    def manually_mark_task_cancelled(self, task_id: int, error_message: str = "Cancelled by user") -> bool:
        """
        Manually mark a task as cancelled (used for running tasks that were cancelled).

        Args:
            task_id: ID of the task to mark as cancelled
            error_message: Optional error message

        Returns:
            True if task was updated, False otherwise
        """
        with self.lock:
            task = self._get_task_by_id(task_id)
            if not task:
                return False

            task["status"] = TaskState.CANCELLED
            task["completion_time"] = time.time()
            task["error"] = error_message

            # Reset current task ID if this was it
            if self.current_task_id == task_id:
                self.current_task_id = None

            # Notify status change
            self._notify_status_changed()
            return True

    def _get_task_by_id(self, task_id: int) -> Optional[Dict[str, Any]]:
        """
        Get a task by ID from the tasks list.

        Args:
            task_id: ID of the task to get

        Returns:
            Task dictionary or None if not found
        """
        for task in self.tasks:
            if task["id"] == task_id:
                return task
        return None

    def _rebuild_queue(self) -> None:
        """
        Rebuild the queue from the tasks list.

        This ensures the queue only contains tasks that are still queued.
        """
        # Create a new queue
        new_queue = queue.Queue()

        # Add all queued tasks to the new queue
        for task in self.tasks:
            if task["status"] == TaskState.QUEUED:
                new_queue.put(task["id"])

        # Replace the old queue
        self.process_queue = new_queue

    def _peek_queue(self) -> Optional[int]:
        """
        Peek at the next item in the queue without removing it.

        Returns:
            Next task ID or None if queue is empty
        """
        try:
            # Simplified queue peeking implementation
            if self.process_queue.empty():
                return None

            # Direct access to underlying queue - more efficient than the previous approach
            if hasattr(self.process_queue, 'queue') and len(self.process_queue.queue) > 0:
                return self.process_queue.queue[0]

            # Fall back to a more reliable method if direct access fails
            temp_q = queue.Queue()
            result = None

            try:
                item = self.process_queue.get_nowait()
                result = item
                temp_q.put(item)
            except queue.Empty:
                return None
            finally:
                # Restore any items we took out
                while not temp_q.empty():
                    self.process_queue.put(temp_q.get())

            return result
        except Exception as e:
            print(f"Error peeking at queue: {e}")
            return None

    def _notify_status_changed(self) -> None:
        """Notify all registered callbacks of a status change."""
        # Check if we're already processing an update or if it's too soon for another update
        current_time = time.time()
        with self.lock:
            # Increase throttling to reduce UI updates even further (2 seconds between updates)
            if self.is_updating or (current_time - self.last_update_time < 2.0):  # Throttle to max 1 update per 2 seconds
                return
            self.is_updating = True
            self.last_update_time = current_time

        try:
            # Schedule callbacks with a longer delay to allow batching of multiple updates
            # This helps prevent rapid successive updates that cause flickering
            if hasattr(self, '_scheduled_callback') and self._scheduled_callback:
                return  # Already have a scheduled callback

            # Store callbacks to execute
            self._pending_callbacks = self.status_changed_callbacks.copy()

            # Find the first valid callback with a root attribute to schedule the update
            for callback in self._pending_callbacks:
                try:
                    # Try to find the app instance that has the callback
                    if hasattr(callback, '__self__') and hasattr(callback.__self__, 'app') and hasattr(callback.__self__.app, 'root'):
                        root = callback.__self__.app.root
                        # Increase delay to 500ms to further reduce flickering
                        self._scheduled_callback = root.after(500, self._execute_callbacks)
                        break
                except Exception:
                    pass
        finally:
            # Always reset the updating flag when done
            with self.lock:
                self.is_updating = False

    def _execute_callbacks(self):
        """Execute the pending callbacks after a delay"""
        try:
            # Reset the scheduled callback flag
            self._scheduled_callback = None

            # Execute all pending callbacks
            if hasattr(self, '_pending_callbacks'):
                for callback in self._pending_callbacks:
                    try:
                        callback()
                    except Exception as e:
                        print(f"Error in status change callback: {e}")

                # Clear pending callbacks
                self._pending_callbacks = []
        except Exception as e:
            print(f"Error executing callbacks: {e}")