"""
Project manager for the Tire Panorama Tool.

Handles operations related to tire projects, including CRUD operations.
"""
import os
import json
import shutil
from datetime import datetime

from models.tire_project import TireProject

class ProjectManager:
    """
    Manages tire projects for the application.
    """
    def __init__(self, projects_dir):
        """
        Initialize the project manager.

        Args:
            projects_dir: Directory for storing projects
        """
        self.projects_dir = projects_dir

        # Create projects directory if it doesn't exist
        os.makedirs(self.projects_dir, exist_ok=True)

    def load_projects(self):
        """
        Load all projects from the projects directory.

        Returns:
            List of TireProject objects
        """
        projects = []

        # Scan projects directory
        for project_name in os.listdir(self.projects_dir):
            project_dir = os.path.join(self.projects_dir, project_name)

            # Skip non-directories
            if not os.path.isdir(project_dir):
                continue

            # Check for project.json file
            project_file = os.path.join(project_dir, "project.json")
            if not os.path.exists(project_file):
                continue

            try:
                # Load project data
                with open(project_file, 'r') as f:
                    project_data = json.load(f)

                # Add project directory to data
                project_data["project_dir"] = project_dir

                # Create project object
                project = TireProject.from_dict(project_data)
                projects.append(project)
            except Exception as e:
                print(f"Error loading project {project_name}: {e}")

        # Sort projects by creation date (newest first)
        projects.sort(key=lambda p: p.created_date, reverse=True)

        return projects

    def get_project(self, project_id):
        """
        Get a project by ID.

        Args:
            project_id: ID of the project to get

        Returns:
            TireProject object or None if not found
        """
        # First check if the project directory exists
        for project_name in os.listdir(self.projects_dir):
            project_dir = os.path.join(self.projects_dir, project_name)

            # Skip non-directories
            if not os.path.isdir(project_dir):
                continue

            # Check for project.json file
            project_file = os.path.join(project_dir, "project.json")
            if not os.path.exists(project_file):
                continue

            try:
                # Load project data
                with open(project_file, 'r') as f:
                    project_data = json.load(f)

                # Check if this is the project we're looking for
                if project_data.get("id") == project_id:
                    # Add project directory to data
                    project_data["project_dir"] = project_dir

                    # Create project object
                    return TireProject.from_dict(project_data)
            except Exception as e:
                print(f"Error loading project {project_name}: {e}")

        return None

    def get_projects_by_plan(self, plan_id):
        """
        Get all projects using a specific plan.

        Args:
            plan_id: ID of the plan

        Returns:
            List of TireProject objects
        """
        projects = self.load_projects()
        matching_projects = [p for p in projects if p.template_id == plan_id]
        return matching_projects

    def create_project(self, serial_number, plan_id, diameter=None, circumference=None, width=None):
        """
        Create a new project.

        Args:
            serial_number: Serial number of the tire
            plan_id: ID of the plan to use
            diameter: Tire diameter in mm (optional)
            circumference: Tire circumference in mm (optional)
            width: Tire width in mm (optional)


        Returns:
            Created TireProject object or None if creation failed
        """
        try:
            # Create a safe directory name from the serial number
            safe_name = self._create_safe_directory_name(serial_number)

            # Create a unique directory name
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            project_dir_name = f"{safe_name}_{timestamp}"
            project_dir = os.path.join(self.projects_dir, project_dir_name)

            # Create the project directory
            os.makedirs(project_dir, exist_ok=True)

            # Create subproject directories
            for subproject in ["frontal", "left", "right"]:
                subproject_dir = os.path.join(project_dir, subproject)
                os.makedirs(subproject_dir, exist_ok=True)

                # Create frames and output directories
                frames_dir = os.path.join(subproject_dir, "frames")
                output_dir = os.path.join(subproject_dir, "output")
                os.makedirs(frames_dir, exist_ok=True)
                os.makedirs(output_dir, exist_ok=True)

            # Create the project object with dimensions
            project = TireProject(
                serial_number,
                plan_id,
                project_dir,
                diameter=diameter,
                circumference=circumference,
                width=width
            )

            # If diameter is provided but circumference is not, calculate it
            if diameter is not None and circumference is None:
                project.calculate_circumference()

            # If circumference is provided but diameter is not, calculate it
            elif circumference is not None and diameter is None:
                project.calculate_diameter()

            # Save the project
            self._save_project_file(project)

            return project
        except Exception as e:
            print(f"Error creating project: {e}")
            return None

    def update_project(self, project_id, serial_number=None, plan_id=None, diameter=None,
                      circumference=None, width=None):
        """
        Update an existing project.

        Args:
            project_id: ID of the project to update
            serial_number: New serial number (optional)
            plan_id: New plan ID (optional)
            diameter: New diameter (optional)
            circumference: New circumference (optional)
            width: New width (optional)


        Returns:
            Updated TireProject if successful, None otherwise
        """
        # Get the project
        project = self.get_project(project_id)
        if not project:
            return None

        # Update the project
        if project.update(serial_number, plan_id, diameter, circumference, width):
            # Save the project
            self._save_project_file(project)

        return project

    def delete_project(self, project_id):
        """
        Delete a project.

        Args:
            project_id: ID of the project to delete

        Returns:
            True if successful, False otherwise
        """
        # Get the project
        project = self.get_project(project_id)
        if not project:
            return False

        try:
            # Delete the project directory
            if os.path.exists(project.project_dir):
                shutil.rmtree(project.project_dir)
                return True
            else:
                return False
        except Exception as e:
            print(f"Error deleting project: {e}")
            return False

    def update_subproject(self, project_id, subproject_type, status=None, input_path=None, input_type=None, parameters=None):
        """
        Update a subproject.

        Args:
            project_id: ID of the parent project
            subproject_type: Type of subproject ("frontal", "left", or "right")
            status: New status (optional)
            input_path: New input path (optional)
            input_type: New input type (optional)
            parameters: New parameters (optional)

        Returns:
            Updated TireProject if successful, None otherwise
        """
        # Get the project
        project = self.get_project(project_id)
        if not project:
            return None

        # Update the subproject
        if project.update_subproject(subproject_type, status, input_path, input_type, parameters):
            # Save the project
            self._save_project_file(project)

        return project

    def save_project(self, project):
        """
        Save an existing project.

        Args:
            project: TireProject object to save

        Returns:
            True if successful, False otherwise
        """
        return self._save_project_file(project)

    def _save_project_file(self, project):
        """
        Save a project's metadata to its project.json file.

        Args:
            project: TireProject object to save

        Returns:
            True if successful, False otherwise
        """
        try:
            # Create the project file path
            project_file = os.path.join(project.project_dir, "project.json")

            # Create a backup of the current file if it exists
            if os.path.exists(project_file):
                backup_file = f"{project_file}.bak"
                shutil.copy2(project_file, backup_file)

            # Write project data to file
            with open(project_file, 'w') as f:
                json.dump(project.to_dict(), f, indent=2)

            return True
        except Exception as e:
            print(f"Error saving project: {e}")
            return False

    def _create_safe_directory_name(self, serial_number):
        """
        Create a safe directory name from a serial number.

        Args:
            serial_number: Tire serial number

        Returns:
            Safe directory name
        """
        # Replace invalid characters with underscores
        safe_name = ''.join([c if c.isalnum() or c in [' ', '_', '-'] else '_' for c in serial_number])

        # Replace spaces with underscores
        safe_name = safe_name.replace(' ', '_')

        # Limit length
        if len(safe_name) > 30:
            safe_name = safe_name[:30]

        # Ensure it doesn't start with a dash or underscore
        if safe_name.startswith('-') or safe_name.startswith('_'):
            safe_name = 'project' + safe_name

        # Ensure it's not empty
        if not safe_name:
            safe_name = 'project'

        return safe_name