"""
Plan manager for the Tire Panorama Tool.

Handles operations related to test plans, including CRUD operations.
"""
import os
import json
import shutil
from utils.error_handler import <PERSON>rrorHandler  # Import ErrorHandler

from models.test_plan import TestPlan

class PlanManager:
    """
    Manages test plans for the application.
    """
    def __init__(self, plans_dir):
        """
        Initialize the plan manager.

        Args:
            plans_dir: Directory for storing plans
        """
        self.plans_dir = plans_dir
        self.plans_file = os.path.join(plans_dir, "plans.json")

        # Create plans directory if it doesn't exist
        os.makedirs(self.plans_dir, exist_ok=True)

    def load_plans(self):
        """
        Load plans from the plans file.

        Returns:
            List of TestPlan objects
        """
        if not os.path.exists(self.plans_file):
            # Create default plans if file doesn't exist
            default_plans = self._create_default_plans()
            self.save_plans(default_plans)
            return default_plans

        try:
            with open(self.plans_file, 'r') as f:
                data = json.load(f)

            plans = []
            for plan_data in data:
                plans.append(TestPlan.from_dict(plan_data))

            # Sort plans by name
            plans.sort(key=lambda t: t.name)

            return plans
        except Exception as e:
            ErrorHandler.log_error(f"Error loading plans: {e}")
            # Create default plans as fallback
            default_plans = self._create_default_plans()
            self.save_plans(default_plans)
            return default_plans

    def save_plans(self, plans):
        """
        Save plans to the plans file.

        Args:
            plans: List of TestPlan objects

        Returns:
            True if successful, False otherwise
        """
        try:
            # Create a backup of the current file if it exists
            if os.path.exists(self.plans_file):
                backup_file = f"{self.plans_file}.bak"
                shutil.copy2(self.plans_file, backup_file)

            # Convert plans to dictionaries
            plan_dicts = [plan.to_dict() for plan in plans]

            # Write plans to file
            with open(self.plans_file, 'w') as f:
                json.dump(plan_dicts, f, indent=2)

            return True
        except Exception as e:
            ErrorHandler.log_error(f"Error saving plans: {e}")
            return False

    def get_plan(self, plan_id):
        """
        Get a plan by ID.

        Args:
            plan_id: ID of the plan to get

        Returns:
            TestPlan object or None if not found
        """
        plans = self.load_plans()
        for plan in plans:
            if plan.id == plan_id:
                return plan
        return None

    def add_plan(self, plan):
        """
        Add a new plan.

        Args:
            plan: TestPlan object to add

        Returns:
            True if successful, False otherwise
        """
        # Load existing plans
        plans = self.load_plans()

        # Check if a plan with the same ID already exists
        for existing_plan in plans:
            if existing_plan.id == plan.id:
                return False

        # Add the new plan
        plans.append(plan)

        # Save plans
        return self.save_plans(plans)

    def update_plan(self, plan_id, name=None, unique_attribute=None):
        """
        Update an existing plan.

        Args:
            plan_id: ID of the plan to update
            name: New name (optional)
            unique_attribute: New unique attribute (optional)

        Returns:
            Updated TestPlan if successful, None otherwise
        """
        # Load existing plans
        plans = self.load_plans()

        # Find the plan to update
        for i, plan in enumerate(plans):
            if plan.id == plan_id:
                # Update the plan
                if plan.update(name, unique_attribute):
                    # Save plans
                    self.save_plans(plans)
                    return plan
                return plan

        return None

    def delete_plan(self, plan_id):
        """
        Delete a plan.

        Args:
            plan_id: ID of the plan to delete

        Returns:
            True if successful, False otherwise
        """
        # Load existing plans
        plans = self.load_plans()

        # Find the plan to delete
        for i, plan in enumerate(plans):
            if plan.id == plan_id:
                # Remove the plan
                plans.pop(i)

                # Save plans
                return self.save_plans(plans)

        return False

    def _create_default_plans(self):
        """
        Create default plans.

        Returns:
            List of default TestPlan objects
        """
        default_plans = [
            TestPlan(name="Standard Car Tire - 205/55R16"),
            TestPlan(name="SUV Tire - 245/65R17"),
            TestPlan(name="Truck Tire - 275/70R18")
        ]

        return default_plans