"""
Project dialogs for the Tire Panorama Tool.

Contains dialogs for project creation and management.
"""
import tkinter as tk
from tkinter import ttk, messagebox

def show_new_project_dialog(app, plan):
    """
    Show dialog to create a new project from a plan.

    Args:
        app: Main application instance
        plan: TestPlan to use

    Returns:
        New TireProject if created, None if cancelled
    """
    # Create dialog window
    dialog = tk.Toplevel(app.root)
    dialog.title(f"New Project - {plan.name}")
    dialog.geometry("450x250")
    dialog.transient(app.root)
    dialog.grab_set()

    # Make dialog modal
    dialog.focus_set()

    # Center dialog
    x = app.root.winfo_x() + (app.root.winfo_width() // 2) - (450 // 2)
    y = app.root.winfo_y() + (app.root.winfo_height() // 2) - (250 // 2)
    dialog.geometry(f"+{x}+{y}")

    # Store result
    result = [None]

    # Create content
    content_frame = ttk.Frame(dialog, padding=10)
    content_frame.pack(fill=tk.BOTH, expand=True)

    # Title
    ttk.Label(
        content_frame,
        text=f"Create New Project from Plan: {plan.name}",
        font=("Segoe UI", 12, "bold")
    ).pack(pady=(0, 10))

    # Plan info
    info_frame = ttk.LabelFrame(content_frame, text="Plan Information")
    info_frame.pack(fill=tk.X, pady=10)

    info_text = ttk.Label(
        info_frame,
        text=f"Plan: {plan.name}",
        wraplength=400,
        padding=10
    )
    info_text.pack(fill=tk.X)

    if hasattr(plan, 'unique_attribute') and plan.unique_attribute:
        extra_text = ttk.Label(
            info_frame,
            text=f"Description: {plan.unique_attribute}",
            wraplength=400,
            padding=(10, 0, 10, 10)
        )
        extra_text.pack(fill=tk.X)

    # Serial Number
    name_frame = ttk.Frame(content_frame)
    name_frame.pack(fill=tk.X, pady=10)

    ttk.Label(name_frame, text="Serial Number:", width=15, anchor=tk.W).pack(side=tk.LEFT)

    # Default serial number
    from datetime import datetime
    default_serial = f"{plan.name}-{datetime.now().strftime('%Y%m%d%H%M')}"

    name_var = tk.StringVar(value=default_serial)
    name_entry = ttk.Entry(name_frame, textvariable=name_var, width=30)
    name_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
    name_entry.focus_set()  # Set focus to the entry
    name_entry.select_range(0, tk.END)  # Select all text

    # Buttons
    buttons_frame = ttk.Frame(content_frame)
    buttons_frame.pack(fill=tk.X, pady=10)

    def on_cancel():
        dialog.destroy()

    def on_create():
        # Validate
        serial_number = name_var.get().strip()
        if not serial_number:
            messagebox.showerror("Error", "Serial number cannot be empty", parent=dialog)
            return

        # Create project
        try:
            project = app.project_manager.create_project(serial_number, plan.id)
            if project:
                result[0] = project
                dialog.destroy()
            else:
                messagebox.showerror("Error", "Failed to create ETO / SID", parent=dialog)
        except Exception as e:
            messagebox.showerror("Error", f"Error creating ETO / SID: {str(e)}", parent=dialog)

    ttk.Button(buttons_frame, text="Cancel", command=on_cancel).pack(side=tk.RIGHT, padx=5)
    ttk.Button(buttons_frame, text="Create ETO / SID", command=on_create, style="Accent.TButton").pack(side=tk.RIGHT, padx=5)

    # Bind Enter key
    dialog.bind("<Return>", lambda event: on_create())
    dialog.bind("<Escape>", lambda event: on_cancel())

    # Wait for dialog to close
    app.root.wait_window(dialog)

    return result[0]

def show_rename_project_dialog(app, project):
    """
    Show dialog to update a project's serial number.

    Args:
        app: Main application instance
        project: TireProject to update

    Returns:
        True if updated, False if cancelled
    """
    # Create dialog window
    dialog = tk.Toplevel(app.root)
    dialog.title("Update Serial Number")
    dialog.geometry("400x150")
    dialog.transient(app.root)
    dialog.grab_set()

    # Make dialog modal
    dialog.focus_set()

    # Center dialog
    x = app.root.winfo_x() + (app.root.winfo_width() // 2) - (400 // 2)
    y = app.root.winfo_y() + (app.root.winfo_height() // 2) - (150 // 2)
    dialog.geometry(f"+{x}+{y}")

    # Store result
    result = [False]

    # Create content
    content_frame = ttk.Frame(dialog, padding=10)
    content_frame.pack(fill=tk.BOTH, expand=True)

    # Title
    ttk.Label(
        content_frame,
        text="Update Serial Number",
        font=("Segoe UI", 12, "bold")
    ).pack(pady=(0, 10))

    # Serial Number
    name_frame = ttk.Frame(content_frame)
    name_frame.pack(fill=tk.X, pady=10)

    ttk.Label(name_frame, text="New Serial #:", width=15, anchor=tk.W).pack(side=tk.LEFT)

    name_var = tk.StringVar(value=project.serial_number)
    name_entry = ttk.Entry(name_frame, textvariable=name_var, width=30)
    name_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
    name_entry.focus_set()  # Set focus to the entry
    name_entry.select_range(0, tk.END)  # Select all text

    # Buttons
    buttons_frame = ttk.Frame(content_frame)
    buttons_frame.pack(fill=tk.X, pady=10)

    def on_cancel():
        dialog.destroy()

    def on_rename():
        # Validate
        new_serial = name_var.get().strip()
        if not new_serial:
            messagebox.showerror("Error", "Serial number cannot be empty", parent=dialog)
            return

        if new_serial == project.serial_number:
            dialog.destroy()
            return

        # Update serial number
        try:
            app.project_manager.update_project(project.id, serial_number=new_serial)
            result[0] = True
            dialog.destroy()
        except Exception as e:
            messagebox.showerror("Error", f"Error renaming ETO / SID: {str(e)}", parent=dialog)

    ttk.Button(buttons_frame, text="Cancel", command=on_cancel).pack(side=tk.RIGHT, padx=5)
    ttk.Button(buttons_frame, text="Update", command=on_rename, style="Accent.TButton").pack(side=tk.RIGHT, padx=5)

    # Bind Enter key
    dialog.bind("<Return>", lambda event: on_rename())
    dialog.bind("<Escape>", lambda event: on_cancel())

    # Wait for dialog to close
    app.root.wait_window(dialog)

    return result[0]

def show_plan_selection_dialog(app):
    """
    Show dialog to select a plan for a new project.

    Args:
        app: Main application instance

    Returns:
        Selected TestPlan or None if cancelled
    """
    # Create dialog window
    dialog = tk.Toplevel(app.root)
    dialog.title("Select Plan")
    dialog.geometry("500x400")
    dialog.transient(app.root)
    dialog.grab_set()

    # Make dialog modal
    dialog.focus_set()

    # Center dialog
    x = app.root.winfo_x() + (app.root.winfo_width() // 2) - (500 // 2)
    y = app.root.winfo_y() + (app.root.winfo_height() // 2) - (400 // 2)
    dialog.geometry(f"+{x}+{y}")

    # Store result
    result = [None]

    # Create content
    content_frame = ttk.Frame(dialog, padding=10)
    content_frame.pack(fill=tk.BOTH, expand=True)

    # Title
    ttk.Label(
        content_frame,
        text="Select Plan for New Project",
        font=("Segoe UI", 12, "bold")
    ).pack(pady=(0, 10))

    # Plans list
    list_frame = ttk.Frame(content_frame)
    list_frame.pack(fill=tk.BOTH, expand=True, pady=10)

    # Create treeview for plans
    columns = ("name", "description")
    plans_tree = ttk.Treeview(
        list_frame,
        columns=columns,
        show="headings",
        selectmode="extended"  # Changed from "browse" to "extended" to allow multiple selection
    )

    # Configure columns
    plans_tree.heading("name", text="Plan Name")
    plans_tree.heading("description", text="Description")

    plans_tree.column("name", width=200, anchor=tk.W)
    plans_tree.column("description", width=250, anchor=tk.W)

    # Add scrollbar
    scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=plans_tree.yview)
    plans_tree.configure(yscrollcommand=scrollbar.set)

    # Pack widgets
    plans_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    # Add plans
    for plan in app.plans:
        description = plan.unique_attribute if hasattr(plan, 'unique_attribute') and plan.unique_attribute else "-"

        plans_tree.insert(
            "", "end",
            values=(plan.name, description),
            tags=(plan.id,)
        )

    # Select first plan
    if plans_tree.get_children():
        plans_tree.selection_set(plans_tree.get_children()[0])
        plans_tree.focus(plans_tree.get_children()[0])

    # Buttons
    buttons_frame = ttk.Frame(content_frame)
    buttons_frame.pack(fill=tk.X, pady=10)

    def on_cancel():
        dialog.destroy()

    def on_select():
        selection = plans_tree.selection()
        if not selection:
            messagebox.showerror("Error", "Please select a plan", parent=dialog)
            return

        plan_id = plans_tree.item(selection[0], "tags")[0]

        # Find plan
        for plan in app.plans:
            if plan.id == plan_id:
                result[0] = plan
                dialog.destroy()
                return

        messagebox.showerror("Error", "Selected plan not found", parent=dialog)

    ttk.Button(buttons_frame, text="Cancel", command=on_cancel).pack(side=tk.RIGHT, padx=5)
    ttk.Button(buttons_frame, text="Select", command=on_select, style="Accent.TButton").pack(side=tk.RIGHT, padx=5)

    # Double-click to select
    plans_tree.bind("<Double-1>", lambda event: on_select())

    # Bind Enter key
    dialog.bind("<Return>", lambda event: on_select())
    dialog.bind("<Escape>", lambda event: on_cancel())

    # Wait for dialog to close
    app.root.wait_window(dialog)

    return result[0]