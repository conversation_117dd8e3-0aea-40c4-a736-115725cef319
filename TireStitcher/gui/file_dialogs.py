"""
File dialog utilities for the Tire Panorama Tool.
"""
import os
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from gui.utils import shorten_path

def ask_project_name_and_location(self, default_name):
    """Ask the user for a project name and location with a default suggestion"""
    dialog = tk.Toplevel(self.root)
    dialog.title("ETO / SID Name and Location")
    dialog.geometry("500x200")
    dialog.resizable(False, False)
    dialog.transient(self.root)
    dialog.grab_set()

    # Center dialog
    dialog.update_idletasks()
    width = dialog.winfo_width()
    height = dialog.winfo_height()
    x = (dialog.winfo_screenwidth() // 2) - (width // 2)
    y = (dialog.winfo_screenheight() // 2) - (height // 2)
    dialog.geometry(f"{width}x{height}+{x}+{y}")

    result = [None]  # Use list to store result

    # Project name
    name_frame = ttk.Frame(dialog)
    name_frame.pack(fill=tk.X, padx=20, pady=(15, 5))

    ttk.Label(
        name_frame,
        text="ETO / SID name:",
        width=15
    ).pack(side=tk.LEFT)

    name_var = tk.StringVar(value=default_name)
    name_entry = ttk.Entry(name_frame, textvariable=name_var, width=40)
    name_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
    name_entry.select_range(0, len(default_name))
    name_entry.icursor(len(default_name))
    name_entry.focus_set()

    # Project location - Important: Use two variables!
    location_frame = ttk.Frame(dialog)
    location_frame.pack(fill=tk.X, padx=20, pady=5)

    ttk.Label(
        location_frame,
        text="ETO / SID location:",
        width=15
    ).pack(side=tk.LEFT)

    # Actual path value (hidden) - this is what will be used for the project
    actual_location_var = tk.StringVar(value=self.projects_dir)

    # Display variable for the entry field
    display_location_var = tk.StringVar()
    location_entry = ttk.Entry(location_frame, textvariable=display_location_var, width=40)
    location_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))

    # Initial display the shortened path (but keep the full path in actual_location_var)
    display_location_var.set(shorten_path(self, actual_location_var.get()))

    def browse_location():
        # Make dialog modal to the dialog (parent), not to the main window
        location = filedialog.askdirectory(
            title="Select ETO / SID Location",
            initialdir=actual_location_var.get(),  # Use the actual full path for initialdir
            parent=dialog
        )
        if location:
            # Update both variables
            actual_location_var.set(location)  # Store the actual full path
            display_location_var.set(shorten_path(self, location))  # Display the shortened version

    ttk.Button(
        location_frame,
        text="Browse...",
        command=browse_location
    ).pack(side=tk.LEFT)

    # Info label
    ttk.Label(
        dialog,
        text="ETO / SID entries will be saved in a subfolder with the specified name.",
        font=("Segoe UI", 9),
        foreground="#555555"
    ).pack(pady=(0, 10), padx=20, anchor=tk.W)

    # Buttons frame
    btn_frame = ttk.Frame(dialog)
    btn_frame.pack(pady=(10, 15), fill=tk.X, padx=20)

    # Cancel button
    ttk.Button(
        btn_frame,
        text="Cancel",
        command=dialog.destroy
    ).pack(side=tk.RIGHT, padx=(5, 0))

    # OK button
    def on_ok():
        project_name = name_var.get().strip()
        project_location = actual_location_var.get().strip()  # Use the actual full path

        if not project_name:
            messagebox.showerror("Error", "ETO / SID name cannot be empty.", parent=dialog)
            return

        # Check for invalid characters
        invalid_chars = ['<', '>', ':', '"', '/', '\\', '|', '?', '*']
        if any(c in project_name for c in invalid_chars):
            messagebox.showerror(
                "Error",
                f"ETO / SID name contains invalid characters: {', '.join(invalid_chars)}",
                parent=dialog
            )
            return

        if not os.path.exists(project_location):
            try:
                # Try to create the directory
                os.makedirs(project_location, exist_ok=True)
            except Exception:
                messagebox.showerror("Error", "Cannot create directory at the specified location.", parent=dialog)
                return

        # Full project path
        project_path = os.path.join(project_location, project_name)

        # Check if project already exists
        if os.path.exists(project_path):
            overwrite = messagebox.askyesno(
                "ETO / SID exists",
                f"An ETO / SID named '{project_name}' already exists at the specified location. Overwrite?",
                parent=dialog
            )
            if not overwrite:
                return

        result[0] = (project_name, project_path)
        dialog.destroy()

    ttk.Button(
        btn_frame,
        text="OK",
        command=on_ok,
        style="Accent.TButton"
    ).pack(side=tk.RIGHT, padx=(0, 5))

    # Handle Enter key
    name_entry.bind("<Return>", lambda event: on_ok())

    # Wait for dialog closure
    self.root.wait_window(dialog)
    return result[0]