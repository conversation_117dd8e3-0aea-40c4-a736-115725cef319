"""
Project card component for the Tire Panorama Tool.

A reusable UI component for displaying a project in the gallery.
"""
import os
import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime

class ProjectCard:
    """
    A card-style component for displaying a project in the gallery.

    This provides a more visual representation of a project than a simple
    list or tree view, with status indicators and action buttons.
    """
    def __init__(self, parent, app, project):
        """
        Initialize the project card.

        Args:
            parent: Parent widget
            app: Main application instance
            project: TireProject instance to display
        """
        self.parent = parent
        self.app = app
        self.project = project

        # Create the frame
        self.frame = ttk.Frame(parent, style="Card.TFrame")
        self.frame.bind("<Button-1>", self._on_click)

        # Create UI
        self._create_ui()

    def _create_ui(self):
        """Create the card UI"""
        # Card header
        header_frame = ttk.Frame(self.frame, style="CardHeader.TFrame")
        header_frame.pack(fill=tk.X)

        # Project serial number
        name_label = ttk.Label(
            header_frame,
            text=self.project.serial_number,
            font=("Segoe UI", 11, "bold"),
            style="CardHeader.TLabel"
        )
        name_label.pack(side=tk.LEFT, padx=10, pady=5)

        # Format date
        try:
            date_obj = datetime.fromisoformat(self.project.created_date)
            date_str = date_obj.strftime("%Y-%m-%d")
        except:
            date_str = "Unknown date"

        # Date label
        date_label = ttk.Label(
            header_frame,
            text=date_str,
            style="CardHeader.TLabel"
        )
        date_label.pack(side=tk.RIGHT, padx=10, pady=5)

        # Card content
        content_frame = ttk.Frame(self.frame)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # Get plan name
        plan_name = "Unknown Plan"
        for plan in self.app.plans:
            if plan.id == self.project.template_id:
                plan_name = plan.name
                break

        # Plan info
        plan_frame = ttk.Frame(content_frame)
        plan_frame.pack(fill=tk.X, pady=2)

        ttk.Label(plan_frame, text="Plan:", width=10, anchor=tk.W).pack(side=tk.LEFT)
        ttk.Label(plan_frame, text=plan_name).pack(side=tk.LEFT)

        # Status indicators
        status_frame = ttk.Frame(content_frame)
        status_frame.pack(fill=tk.X, pady=5)

        # Create indicator for each subproject
        self._create_status_indicator(status_frame, "Frontal", self.project.subprojects.get("frontal", {}))
        self._create_status_indicator(status_frame, "Left", self.project.subprojects.get("left", {}))
        self._create_status_indicator(status_frame, "Right", self.project.subprojects.get("right", {}))

        # Action buttons
        buttons_frame = ttk.Frame(content_frame)
        buttons_frame.pack(fill=tk.X, pady=5)

        # Open button
        open_btn = ttk.Button(
            buttons_frame,
            text="Open",
            command=self._on_open,
            width=10
        )
        open_btn.pack(side=tk.LEFT, padx=2)

        # View button (if any panorama is completed)
        if any(subproject.get("status") == "completed" for subproject in self.project.subprojects.values()):
            view_btn = ttk.Button(
                buttons_frame,
                text="View",
                command=self._on_view,
                width=10
            )
            view_btn.pack(side=tk.LEFT, padx=2)

        # Delete button
        delete_btn = ttk.Button(
            buttons_frame,
            text="Delete",
            command=self._on_delete,
            width=10
        )
        delete_btn.pack(side=tk.RIGHT, padx=2)

    def _create_status_indicator(self, parent, label_text, subproject):
        """Create a status indicator for a subproject"""
        frame = ttk.Frame(parent)
        frame.pack(side=tk.LEFT, padx=5)

        # Get status
        status = subproject.get("status", "not_started")

        # Map status to color and text
        color_map = {
            "not_started": "#888888",  # Gray
            "processing": "#FFA500",   # Orange
            "cancelled": "#FF6347",    # Tomato
            "completed": "#32CD32",    # Lime green
            "failed": "#FF0000"        # Red
        }

        text_map = {
            "not_started": "Not Started",
            "processing": "Processing",
            "cancelled": "Cancelled",
            "completed": "Completed",
            "failed": "Failed"
        }

        color = color_map.get(status, "#888888")
        text = text_map.get(status, "Unknown")

        # Create colored indicator circle
        indicator = tk.Canvas(frame, width=12, height=12, bg=parent.cget("background"), highlightthickness=0)
        indicator.create_oval(2, 2, 10, 10, fill=color, outline="")
        indicator.pack(side=tk.LEFT)

        # Create label
        ttk.Label(frame, text=f"{label_text}: {text}", font=("Segoe UI", 8)).pack(side=tk.LEFT, padx=2)

    def pack(self, **kwargs):
        """Pack the card"""
        self.frame.pack(**kwargs)

    def grid(self, **kwargs):
        """Grid the card"""
        self.frame.grid(**kwargs)

    def _on_click(self, event):
        """Handle click on the card"""
        # Set as current project
        self.app.current_project = self.project

        # Update the current project display
        if hasattr(self.app, 'update_current_project_display'):
            self.app.update_current_project_display()

        # Switch to current tire tab
        if hasattr(self.app, 'notebook'):
            for i, tab_name in enumerate(self.app.notebook.tabs()):
                if self.app.notebook.tab(tab_name, "text") == "Current Tire":
                    self.app.notebook.select(i)
                    break

    def _on_open(self):
        """Handle open button click"""
        # Same as clicking on the card
        self._on_click(None)

    def _on_view(self):
        """Handle view button click"""
        # Set as current project
        self.app.current_project = self.project

        # Find first completed panorama
        for subproject_type, subproject in self.project.subprojects.items():
            if subproject.get("status") == "completed":
                # Check if panorama exists
                output_dir = os.path.join(
                    self.project.project_dir,
                    subproject_type,
                    "output"
                )

                # Try to find panorama files with the new naming pattern
                panorama_path = None

                # Look for files with pattern: tire_*_full.* or tire_unwrapped_full.*
                try:
                    for file in os.listdir(output_dir):
                        if (file.startswith("tire_") and "_full." in file and
                            any(file.endswith(ext) for ext in [".JPG", ".jpg", ".png"])):
                            panorama_path = os.path.join(output_dir, file)
                            break

                    # If not found, try enhanced version
                    if not panorama_path:
                        for file in os.listdir(output_dir):
                            if (file.startswith("tire_") and "_enhanced." in file and
                                any(file.endswith(ext) for ext in [".JPG", ".jpg", ".png"])):
                                panorama_path = os.path.join(output_dir, file)
                                break
                except Exception as e:
                    print(f"Error finding panorama: {e}")
                    continue

                if panorama_path and os.path.exists(panorama_path):
                    # Open in viewer
                    try:
                        from gui.panorama_display import open_full_size_image
                        open_full_size_image(self.app, panorama_path)
                        return
                    except Exception as e:
                        messagebox.showerror("Error", f"Failed to open panorama: {str(e)}")

        # If we get here, no panorama was found
        messagebox.showinfo("Info", "No completed panorama found.")

    def _on_delete(self):
        """Handle delete button click"""
        # Confirm deletion
        if messagebox.askyesno(
            "Confirm Deletion",
            f"Are you sure you want to delete ETO / SID '{self.project.serial_number}'?\n\n"
            "This will permanently delete all data, including panoramas."
        ):
            project_id = self.project.id

            # Delete the project
            if self.app.project_manager.delete_project(project_id):
                # Update the projects list
                if hasattr(self.app, 'update_projects_list'):
                    self.app.update_projects_list()

                # Clear current project if it's the one that was deleted
                if hasattr(self.app, 'current_project') and self.app.current_project and self.app.current_project.id == project_id:
                    self.app.current_project = None

                    # Update the current project display
                    if hasattr(self.app, 'update_current_project_display'):
                        self.app.update_current_project_display()

                # Update status
                self.app.status_var.set(f"Project '{self.project.serial_number}' deleted")
            else:
                # Update status
                self.app.status_var.set(f"Failed to delete project '{self.project.serial_number}'")

            # Remove this card
            self.frame.destroy()