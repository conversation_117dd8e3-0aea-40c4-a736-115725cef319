#include "image_loader.h"
#include "config.h"

#include <regex>
#include <iostream> // Added for logging

// Define DEBUG_PATH_LOGGING if not already defined by build system
// #define DEBUG_PATH_LOGGING

ImageLoader::ImageLoader(int numThreads, const StitchConfig& cfg) : stop(false), config(cfg) {
    for (int i = 0; i < numThreads; i++) {
        workers.emplace_back([this] {
            while (true) {
                std::pair<Path, std::function<void(cv::Mat)>> task;
                {
                    std::unique_lock<std::mutex> lock(queueMutex);
                    condition.wait(lock, [this] {
                        return stop || !taskQueue.empty();
                    });

                    if (stop && taskQueue.empty()) {
                        return;
                    }

                    task = std::move(taskQueue.front());
                    taskQueue.pop();
                }

                // Load the image and process it
#ifdef DEBUG_PATH_LOGGING
                std::cout << "DEBUG_PATH_LOGGING: Loading image: " << task.first.string() << std::endl;
#endif
                cv::Mat img = cv::imread(task.first.string());

                // Resize if needed
                if (!img.empty() && config.resizeScale != 1.0) {
                    img = resizeImageForProcessing(img, config.resizeScale);
                }

                // Call the callback with the loaded image
                task.second(img);
            }
        });
    }
}

ImageLoader::~ImageLoader() {
    {
        std::unique_lock<std::mutex> lock(queueMutex);
        stop = true;
    }
    condition.notify_all();
    for (auto& worker : workers) {
        if (worker.joinable()) {
            worker.join();
        }
    }
}

void ImageLoader::enqueue(const Path& imagePath, std::function<void(cv::Mat)> callback) {
#ifdef DEBUG_PATH_LOGGING
    std::cout << "DEBUG_PATH_LOGGING: Enqueuing image: " << imagePath.string() << std::endl;
#endif
    {
        std::unique_lock<std::mutex> lock(queueMutex);
        taskQueue.emplace(imagePath, callback);
    }
    condition.notify_one();
}

cv::Mat resizeImageForProcessing(const cv::Mat& image, double scaleFactor) {
    if (image.empty()) return image;

    cv::Mat resized;
    cv::resize(image, resized, cv::Size(), scaleFactor, scaleFactor, cv::INTER_AREA);
    return resized;
}

bool naturalSortComparator(const Path& a, const Path& b) {
    std::string sa = a.filename();
    std::string sb = b.filename();

    // Extract numbers and text parts
    std::regex re("([0-9]+)");

    // Iterator for string a
    auto it_a_begin = std::sregex_iterator(sa.begin(), sa.end(), re);
    auto it_a_end = std::sregex_iterator();

    // Iterator for string b
    auto it_b_begin = std::sregex_iterator(sb.begin(), sb.end(), re);
    auto it_b_end = std::sregex_iterator();

    // Compare parts
    auto it_a = it_a_begin;
    auto it_b = it_b_begin;

    while (it_a != it_a_end && it_b != it_b_end) {
        // Get positions of the matches
        size_t pos_a = it_a->position();
        size_t pos_b = it_b->position();

        // If positions differ, compare the text before the numbers
        if (pos_a != pos_b) {
            return sa.substr(0, pos_a) < sb.substr(0, pos_b);
        }

        // Extract the matched numbers
        int num_a = std::stoi(it_a->str());
        int num_b = std::stoi(it_b->str());

        // If numbers differ, compare them
        if (num_a != num_b) {
            return num_a < num_b;
        }

        // Move to next match
        ++it_a;
        ++it_b;
    }

    // If we've compared all numbers and they're equal,
    // either use the remaining text or overall string length
    return sa < sb;
}