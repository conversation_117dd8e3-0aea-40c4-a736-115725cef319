"""
Test plan model for the Tire Panorama Tool.

Defines the TestPlan class for storing tire specifications.
"""
import uuid
import json
from datetime import datetime

class TestPlan:
    """
    Class representing a test plan with specifications.

    Plans now only store a name and ID for categorizing
    tire projects. The actual dimensions are stored with the
    projects themselves.
    """
    def __init__(self, name, plan_id=None, unique_attribute=None):
        """
        Initialize a new test plan.

        Args:
            name: Name of the test plan
            plan_id: Unique ID (generated if not provided)
            unique_attribute: Optional additional identifier
        """
        self.name = name
        self.id = plan_id if plan_id else str(uuid.uuid4())
        self.unique_attribute = unique_attribute
        self.created_date = datetime.now().isoformat()
        self.modified_date = self.created_date

    def update(self, name=None, unique_attribute=None):
        """
        Update the test plan properties.

        Args:
            name: New name (optional)
            unique_attribute: New unique attribute (optional)

        Returns:
            True if any property was updated, False otherwise
        """
        updated = False

        if name is not None and name != self.name:
            self.name = name
            updated = True

        if unique_attribute is not None and unique_attribute != self.unique_attribute:
            self.unique_attribute = unique_attribute
            updated = True

        if updated:
            self.modified_date = datetime.now().isoformat()

        return updated

    def to_dict(self):
        """
        Convert the test plan to a dictionary.

        Returns:
            Dictionary representation of the test plan
        """
        return {
            "id": self.id,
            "name": self.name,
            "unique_attribute": self.unique_attribute,
            "created_date": self.created_date,
            "modified_date": self.modified_date
        }

    def to_json(self):
        """
        Convert the test plan to JSON.

        Returns:
            JSON string representation of the test plan
        """
        return json.dumps(self.to_dict(), indent=2)

    @classmethod
    def from_dict(cls, data):
        """
        Create a TestPlan from a dictionary.

        Args:
            data: Dictionary containing test plan data

        Returns:
            TestPlan instance
        """
        plan = cls(
            name=data.get("name", "Unnamed Plan"),
            plan_id=data.get("id"),
            unique_attribute=data.get("unique_attribute")
        )

        # Set dates if available
        if "created_date" in data:
            plan.created_date = data["created_date"]
        if "modified_date" in data:
            plan.modified_date = data["modified_date"]

        return plan

    @classmethod
    def from_json(cls, json_str):
        """
        Create a TestPlan from a JSON string.

        Args:
            json_str: JSON string containing test plan data

        Returns:
            TestPlan instance
        """
        data = json.loads(json_str)
        return cls.from_dict(data)