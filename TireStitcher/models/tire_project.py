"""
Tire project model for the Tire Panorama Tool.

Defines the TireProject class for storing project information.
"""
import os
import uuid
import json
import math
from datetime import datetime

class TireProject:
    """
    Class representing a tire project with subprojects.

    Projects now store dimension data that was previously in templates.
    """
    def __init__(self, serial_number, template_id, project_dir=None, project_id=None,
                 diameter=None, circumference=None, width=None):
        """
        Initialize a new tire project.

        Args:
            serial_number: Serial number of the tire
            template_id: ID of the test plan used (kept as template_id for backward compatibility)
            project_dir: Directory for the project (optional)
            project_id: Unique ID (generated if not provided)
            diameter: Tire diameter in mm (optional)
            circumference: Tire circumference in mm (optional)
            width: Tire width in mm (optional)

        """
        self.serial_number = serial_number
        self.template_id = template_id
        self.project_dir = project_dir
        self.id = project_id if project_id else str(uuid.uuid4())
        self.created_date = datetime.now().isoformat()
        self.modified_date = self.created_date

        # Tire measurements (moved from template to project)
        self.diameter = diameter
        self.circumference = circumference
        self.width = width


        # Initialize subprojects
        self.subprojects = {
            "frontal": {
                "status": "not_started",
                "input_path": None,
                "input_type": None,
                "parameters": {}
            },
            "left": {
                "status": "not_started",
                "input_path": None,
                "input_type": None,
                "parameters": {}
            },
            "right": {
                "status": "not_started",
                "input_path": None,
                "input_type": None,
                "parameters": {}
            }
        }

    @property
    def name(self):
        """
        Property getter for name that returns serial_number for backward compatibility.

        Returns:
            The serial number of the tire
        """
        return self.serial_number

    def calculate_circumference(self):
        """
        Calculate the circumference based on the diameter.

        Returns:
            True if calculation was successful, False otherwise
        """
        if self.diameter is not None:
            self.circumference = math.pi * self.diameter
            return True
        return False

    def calculate_diameter(self):
        """
        Calculate the diameter based on the circumference.

        Returns:
            True if calculation was successful, False otherwise
        """
        if self.circumference is not None:
            self.diameter = self.circumference / math.pi
            return True
        return False

    def update(self, serial_number=None, template_id=None, diameter=None,
                circumference=None, width=None):
        """
        Update the project properties.

        Args:
            serial_number: New serial number (optional)
            template_id: New template ID (optional)
            diameter: New diameter in mm (optional)
            circumference: New circumference in mm (optional)
            width: New width in mm (optional)


        Returns:
            True if any property was updated, False otherwise
        """
        updated = False

        if serial_number is not None and serial_number != self.serial_number:
            self.serial_number = serial_number
            updated = True

        if template_id is not None and template_id != self.template_id:
            self.template_id = template_id
            updated = True

        # Track if diameter or circumference were manually updated
        diameter_updated = False
        circumference_updated = False

        if diameter is not None and diameter != self.diameter:
            self.diameter = diameter
            updated = True
            diameter_updated = True

            # Automatically update circumference when diameter changes
            if self.diameter is not None:
                self.circumference = self.diameter * math.pi

        if circumference is not None and circumference != self.circumference:
            self.circumference = circumference
            updated = True
            circumference_updated = True

            # Automatically update diameter when circumference changes
            # Only if diameter wasn't also explicitly updated in this call
            if not diameter_updated and self.circumference is not None:
                self.diameter = self.circumference / math.pi

        if width is not None and width != self.width:
            self.width = width
            updated = True



        if updated:
            self.modified_date = datetime.now().isoformat()

        return updated

    def update_subproject(self, subproject_type, status=None, input_path=None, input_type=None, parameters=None):
        """
        Update a subproject's properties.

        Args:
            subproject_type: Type of subproject ("frontal", "left", or "right")
            status: New status (optional)
            input_path: New input path (optional)
            input_type: New input type (optional)
            parameters: New parameters (optional)

        Returns:
            True if any property was updated, False otherwise
        """
        if subproject_type not in self.subprojects:
            return False

        subproject = self.subprojects[subproject_type]
        updated = False

        if status is not None and status != subproject.get("status"):
            subproject["status"] = status
            updated = True

        if input_path is not None and input_path != subproject.get("input_path"):
            # Convert to relative path if it's within the project directory
            if self.project_dir and os.path.isabs(input_path):
                try:
                    rel_path = os.path.relpath(input_path, self.project_dir)
                    # Only use relative path if it doesn't start with ..
                    if not rel_path.startswith('..'):
                        input_path = rel_path
                except (ValueError, OSError):
                    # Keep absolute path if relpath fails
                    pass

            subproject["input_path"] = input_path
            updated = True

        if input_type is not None and input_type != subproject.get("input_type"):
            subproject["input_type"] = input_type
            updated = True

        if parameters is not None:
            if "parameters" not in subproject:
                subproject["parameters"] = {}

            for key, value in parameters.items():
                if key not in subproject["parameters"] or subproject["parameters"][key] != value:
                    subproject["parameters"][key] = value
                    updated = True

        if updated:
            subproject["modified_date"] = datetime.now().isoformat()
            self.modified_date = datetime.now().isoformat()

        return updated

    def get_absolute_input_path(self, subproject_type):
        """
        Get the absolute path for a subproject's input.

        Args:
            subproject_type: Type of subproject ("frontal", "left", or "right")

        Returns:
            Absolute input path or None if not set
        """
        if subproject_type not in self.subprojects:
            return None

        input_path = self.subprojects[subproject_type].get("input_path")
        if not input_path:
            return None

        # Return absolute path if already absolute
        if os.path.isabs(input_path):
            return input_path

        # Convert relative path to absolute if project_dir is available
        if self.project_dir:
            return os.path.abspath(os.path.join(self.project_dir, input_path))

        return input_path

    def get_subproject_state(self, subproject_type):
        """
        Get the processing state of a subproject.

        Args:
            subproject_type: Type of subproject ("frontal", "left", or "right")

        Returns:
            Dictionary with processing state or None if not available
        """
        if subproject_type not in self.subprojects:
            return None

        state_path = os.path.join(self.project_dir, subproject_type, "processing_state.json")
        if not os.path.exists(state_path):
            return None

        try:
            with open(state_path, 'r') as f:
                return json.load(f)
        except:
            return None

    def can_continue_subproject(self, subproject_type):
        """
        Check if a subproject can be continued.

        Args:
            subproject_type: Type of subproject ("frontal", "left", or "right")

        Returns:
            True if the subproject can be continued, False otherwise
        """
        if subproject_type not in self.subprojects:
            return False

        subproject = self.subprojects[subproject_type]
        if subproject.get("status") != "cancelled":
            return False

        return self.get_subproject_state(subproject_type) is not None

    def to_dict(self):
        """
        Convert the project to a dictionary.

        Returns:
            Dictionary representation of the project
        """
        return {
            "id": self.id,
            "serial_number": self.serial_number,
            "template_id": self.template_id,
            "project_dir": self.project_dir,
            "created_date": self.created_date,
            "modified_date": self.modified_date,
            "diameter": self.diameter,
            "circumference": self.circumference,
            "width": self.width,

            "subprojects": self.subprojects
        }

    def to_json(self):
        """
        Convert the project to JSON.

        Returns:
            JSON string representation of the project
        """
        return json.dumps(self.to_dict(), indent=2)

    @classmethod
    def from_dict(cls, data):
        """
        Create a TireProject from a dictionary.

        Args:
            data: Dictionary containing project data

        Returns:
            TireProject instance
        """
        project = cls(
            serial_number=data.get("serial_number", "Unnamed Tire"),
            template_id=data.get("template_id"),
            project_dir=data.get("project_dir"),
            project_id=data.get("id"),
            diameter=data.get("diameter"),
            circumference=data.get("circumference"),
            width=data.get("width")
        )

        # Set dates if available
        if "created_date" in data:
            project.created_date = data["created_date"]
        if "modified_date" in data:
            project.modified_date = data["modified_date"]

        # Set subprojects if available
        if "subprojects" in data:
            project.subprojects = data["subprojects"]

        return project

    @classmethod
    def from_json(cls, json_str):
        """
        Create a TireProject from a JSON string.

        Args:
            json_str: JSON string containing project data

        Returns:
            TireProject instance
        """
        data = json.loads(json_str)
        return cls.from_dict(data)