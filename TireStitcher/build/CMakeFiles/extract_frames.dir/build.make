# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = A:\ReifenScanner\TireStitcher

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = A:\ReifenScanner\TireStitcher\build

# Include any dependencies generated for this target.
include CMakeFiles/extract_frames.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/extract_frames.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/extract_frames.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/extract_frames.dir/flags.make

CMakeFiles/extract_frames.dir/codegen:
.PHONY : CMakeFiles/extract_frames.dir/codegen

CMakeFiles/extract_frames.dir/frame_extraction/main.cpp.obj: CMakeFiles/extract_frames.dir/flags.make
CMakeFiles/extract_frames.dir/frame_extraction/main.cpp.obj: CMakeFiles/extract_frames.dir/includes_CXX.rsp
CMakeFiles/extract_frames.dir/frame_extraction/main.cpp.obj: A:/ReifenScanner/TireStitcher/frame_extraction/main.cpp
CMakeFiles/extract_frames.dir/frame_extraction/main.cpp.obj: CMakeFiles/extract_frames.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=A:\ReifenScanner\TireStitcher\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/extract_frames.dir/frame_extraction/main.cpp.obj"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/extract_frames.dir/frame_extraction/main.cpp.obj -MF CMakeFiles\extract_frames.dir\frame_extraction\main.cpp.obj.d -o CMakeFiles\extract_frames.dir\frame_extraction\main.cpp.obj -c A:\ReifenScanner\TireStitcher\frame_extraction\main.cpp

CMakeFiles/extract_frames.dir/frame_extraction/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/extract_frames.dir/frame_extraction/main.cpp.i"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E A:\ReifenScanner\TireStitcher\frame_extraction\main.cpp > CMakeFiles\extract_frames.dir\frame_extraction\main.cpp.i

CMakeFiles/extract_frames.dir/frame_extraction/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/extract_frames.dir/frame_extraction/main.cpp.s"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S A:\ReifenScanner\TireStitcher\frame_extraction\main.cpp -o CMakeFiles\extract_frames.dir\frame_extraction\main.cpp.s

CMakeFiles/extract_frames.dir/frame_extraction/ffmpeg_extractor.cpp.obj: CMakeFiles/extract_frames.dir/flags.make
CMakeFiles/extract_frames.dir/frame_extraction/ffmpeg_extractor.cpp.obj: CMakeFiles/extract_frames.dir/includes_CXX.rsp
CMakeFiles/extract_frames.dir/frame_extraction/ffmpeg_extractor.cpp.obj: A:/ReifenScanner/TireStitcher/frame_extraction/ffmpeg_extractor.cpp
CMakeFiles/extract_frames.dir/frame_extraction/ffmpeg_extractor.cpp.obj: CMakeFiles/extract_frames.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=A:\ReifenScanner\TireStitcher\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/extract_frames.dir/frame_extraction/ffmpeg_extractor.cpp.obj"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/extract_frames.dir/frame_extraction/ffmpeg_extractor.cpp.obj -MF CMakeFiles\extract_frames.dir\frame_extraction\ffmpeg_extractor.cpp.obj.d -o CMakeFiles\extract_frames.dir\frame_extraction\ffmpeg_extractor.cpp.obj -c A:\ReifenScanner\TireStitcher\frame_extraction\ffmpeg_extractor.cpp

CMakeFiles/extract_frames.dir/frame_extraction/ffmpeg_extractor.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/extract_frames.dir/frame_extraction/ffmpeg_extractor.cpp.i"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E A:\ReifenScanner\TireStitcher\frame_extraction\ffmpeg_extractor.cpp > CMakeFiles\extract_frames.dir\frame_extraction\ffmpeg_extractor.cpp.i

CMakeFiles/extract_frames.dir/frame_extraction/ffmpeg_extractor.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/extract_frames.dir/frame_extraction/ffmpeg_extractor.cpp.s"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S A:\ReifenScanner\TireStitcher\frame_extraction\ffmpeg_extractor.cpp -o CMakeFiles\extract_frames.dir\frame_extraction\ffmpeg_extractor.cpp.s

CMakeFiles/extract_frames.dir/frame_extraction/opencv_extractor.cpp.obj: CMakeFiles/extract_frames.dir/flags.make
CMakeFiles/extract_frames.dir/frame_extraction/opencv_extractor.cpp.obj: CMakeFiles/extract_frames.dir/includes_CXX.rsp
CMakeFiles/extract_frames.dir/frame_extraction/opencv_extractor.cpp.obj: A:/ReifenScanner/TireStitcher/frame_extraction/opencv_extractor.cpp
CMakeFiles/extract_frames.dir/frame_extraction/opencv_extractor.cpp.obj: CMakeFiles/extract_frames.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=A:\ReifenScanner\TireStitcher\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/extract_frames.dir/frame_extraction/opencv_extractor.cpp.obj"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/extract_frames.dir/frame_extraction/opencv_extractor.cpp.obj -MF CMakeFiles\extract_frames.dir\frame_extraction\opencv_extractor.cpp.obj.d -o CMakeFiles\extract_frames.dir\frame_extraction\opencv_extractor.cpp.obj -c A:\ReifenScanner\TireStitcher\frame_extraction\opencv_extractor.cpp

CMakeFiles/extract_frames.dir/frame_extraction/opencv_extractor.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/extract_frames.dir/frame_extraction/opencv_extractor.cpp.i"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E A:\ReifenScanner\TireStitcher\frame_extraction\opencv_extractor.cpp > CMakeFiles\extract_frames.dir\frame_extraction\opencv_extractor.cpp.i

CMakeFiles/extract_frames.dir/frame_extraction/opencv_extractor.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/extract_frames.dir/frame_extraction/opencv_extractor.cpp.s"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S A:\ReifenScanner\TireStitcher\frame_extraction\opencv_extractor.cpp -o CMakeFiles\extract_frames.dir\frame_extraction\opencv_extractor.cpp.s

CMakeFiles/extract_frames.dir/frame_extraction/rotation_analysis.cpp.obj: CMakeFiles/extract_frames.dir/flags.make
CMakeFiles/extract_frames.dir/frame_extraction/rotation_analysis.cpp.obj: CMakeFiles/extract_frames.dir/includes_CXX.rsp
CMakeFiles/extract_frames.dir/frame_extraction/rotation_analysis.cpp.obj: A:/ReifenScanner/TireStitcher/frame_extraction/rotation_analysis.cpp
CMakeFiles/extract_frames.dir/frame_extraction/rotation_analysis.cpp.obj: CMakeFiles/extract_frames.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=A:\ReifenScanner\TireStitcher\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/extract_frames.dir/frame_extraction/rotation_analysis.cpp.obj"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/extract_frames.dir/frame_extraction/rotation_analysis.cpp.obj -MF CMakeFiles\extract_frames.dir\frame_extraction\rotation_analysis.cpp.obj.d -o CMakeFiles\extract_frames.dir\frame_extraction\rotation_analysis.cpp.obj -c A:\ReifenScanner\TireStitcher\frame_extraction\rotation_analysis.cpp

CMakeFiles/extract_frames.dir/frame_extraction/rotation_analysis.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/extract_frames.dir/frame_extraction/rotation_analysis.cpp.i"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E A:\ReifenScanner\TireStitcher\frame_extraction\rotation_analysis.cpp > CMakeFiles\extract_frames.dir\frame_extraction\rotation_analysis.cpp.i

CMakeFiles/extract_frames.dir/frame_extraction/rotation_analysis.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/extract_frames.dir/frame_extraction/rotation_analysis.cpp.s"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S A:\ReifenScanner\TireStitcher\frame_extraction\rotation_analysis.cpp -o CMakeFiles\extract_frames.dir\frame_extraction\rotation_analysis.cpp.s

CMakeFiles/extract_frames.dir/stitching/fs_util.cpp.obj: CMakeFiles/extract_frames.dir/flags.make
CMakeFiles/extract_frames.dir/stitching/fs_util.cpp.obj: CMakeFiles/extract_frames.dir/includes_CXX.rsp
CMakeFiles/extract_frames.dir/stitching/fs_util.cpp.obj: A:/ReifenScanner/TireStitcher/stitching/fs_util.cpp
CMakeFiles/extract_frames.dir/stitching/fs_util.cpp.obj: CMakeFiles/extract_frames.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=A:\ReifenScanner\TireStitcher\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/extract_frames.dir/stitching/fs_util.cpp.obj"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/extract_frames.dir/stitching/fs_util.cpp.obj -MF CMakeFiles\extract_frames.dir\stitching\fs_util.cpp.obj.d -o CMakeFiles\extract_frames.dir\stitching\fs_util.cpp.obj -c A:\ReifenScanner\TireStitcher\stitching\fs_util.cpp

CMakeFiles/extract_frames.dir/stitching/fs_util.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/extract_frames.dir/stitching/fs_util.cpp.i"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E A:\ReifenScanner\TireStitcher\stitching\fs_util.cpp > CMakeFiles\extract_frames.dir\stitching\fs_util.cpp.i

CMakeFiles/extract_frames.dir/stitching/fs_util.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/extract_frames.dir/stitching/fs_util.cpp.s"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S A:\ReifenScanner\TireStitcher\stitching\fs_util.cpp -o CMakeFiles\extract_frames.dir\stitching\fs_util.cpp.s

CMakeFiles/extract_frames.dir/stitching/frame_extraction_utils.cpp.obj: CMakeFiles/extract_frames.dir/flags.make
CMakeFiles/extract_frames.dir/stitching/frame_extraction_utils.cpp.obj: CMakeFiles/extract_frames.dir/includes_CXX.rsp
CMakeFiles/extract_frames.dir/stitching/frame_extraction_utils.cpp.obj: A:/ReifenScanner/TireStitcher/stitching/frame_extraction_utils.cpp
CMakeFiles/extract_frames.dir/stitching/frame_extraction_utils.cpp.obj: CMakeFiles/extract_frames.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=A:\ReifenScanner\TireStitcher\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/extract_frames.dir/stitching/frame_extraction_utils.cpp.obj"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/extract_frames.dir/stitching/frame_extraction_utils.cpp.obj -MF CMakeFiles\extract_frames.dir\stitching\frame_extraction_utils.cpp.obj.d -o CMakeFiles\extract_frames.dir\stitching\frame_extraction_utils.cpp.obj -c A:\ReifenScanner\TireStitcher\stitching\frame_extraction_utils.cpp

CMakeFiles/extract_frames.dir/stitching/frame_extraction_utils.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/extract_frames.dir/stitching/frame_extraction_utils.cpp.i"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E A:\ReifenScanner\TireStitcher\stitching\frame_extraction_utils.cpp > CMakeFiles\extract_frames.dir\stitching\frame_extraction_utils.cpp.i

CMakeFiles/extract_frames.dir/stitching/frame_extraction_utils.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/extract_frames.dir/stitching/frame_extraction_utils.cpp.s"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S A:\ReifenScanner\TireStitcher\stitching\frame_extraction_utils.cpp -o CMakeFiles\extract_frames.dir\stitching\frame_extraction_utils.cpp.s

# Object files for target extract_frames
extract_frames_OBJECTS = \
"CMakeFiles/extract_frames.dir/frame_extraction/main.cpp.obj" \
"CMakeFiles/extract_frames.dir/frame_extraction/ffmpeg_extractor.cpp.obj" \
"CMakeFiles/extract_frames.dir/frame_extraction/opencv_extractor.cpp.obj" \
"CMakeFiles/extract_frames.dir/frame_extraction/rotation_analysis.cpp.obj" \
"CMakeFiles/extract_frames.dir/stitching/fs_util.cpp.obj" \
"CMakeFiles/extract_frames.dir/stitching/frame_extraction_utils.cpp.obj"

# External object files for target extract_frames
extract_frames_EXTERNAL_OBJECTS =

A:/ReifenScanner/extract_frames.exe: CMakeFiles/extract_frames.dir/frame_extraction/main.cpp.obj
A:/ReifenScanner/extract_frames.exe: CMakeFiles/extract_frames.dir/frame_extraction/ffmpeg_extractor.cpp.obj
A:/ReifenScanner/extract_frames.exe: CMakeFiles/extract_frames.dir/frame_extraction/opencv_extractor.cpp.obj
A:/ReifenScanner/extract_frames.exe: CMakeFiles/extract_frames.dir/frame_extraction/rotation_analysis.cpp.obj
A:/ReifenScanner/extract_frames.exe: CMakeFiles/extract_frames.dir/stitching/fs_util.cpp.obj
A:/ReifenScanner/extract_frames.exe: CMakeFiles/extract_frames.dir/stitching/frame_extraction_utils.cpp.obj
A:/ReifenScanner/extract_frames.exe: CMakeFiles/extract_frames.dir/build.make
A:/ReifenScanner/extract_frames.exe: A:/OpenCV-MinGW-Build-OpenCV-4.5.5-x64/x64/mingw/lib/libopencv_gapi455.dll.a
A:/ReifenScanner/extract_frames.exe: A:/OpenCV-MinGW-Build-OpenCV-4.5.5-x64/x64/mingw/lib/libopencv_highgui455.dll.a
A:/ReifenScanner/extract_frames.exe: A:/OpenCV-MinGW-Build-OpenCV-4.5.5-x64/x64/mingw/lib/libopencv_ml455.dll.a
A:/ReifenScanner/extract_frames.exe: A:/OpenCV-MinGW-Build-OpenCV-4.5.5-x64/x64/mingw/lib/libopencv_objdetect455.dll.a
A:/ReifenScanner/extract_frames.exe: A:/OpenCV-MinGW-Build-OpenCV-4.5.5-x64/x64/mingw/lib/libopencv_photo455.dll.a
A:/ReifenScanner/extract_frames.exe: A:/OpenCV-MinGW-Build-OpenCV-4.5.5-x64/x64/mingw/lib/libopencv_stitching455.dll.a
A:/ReifenScanner/extract_frames.exe: A:/OpenCV-MinGW-Build-OpenCV-4.5.5-x64/x64/mingw/lib/libopencv_video455.dll.a
A:/ReifenScanner/extract_frames.exe: A:/OpenCV-MinGW-Build-OpenCV-4.5.5-x64/x64/mingw/lib/libopencv_videoio455.dll.a
A:/ReifenScanner/extract_frames.exe: C:/ProgramData/chocolatey/lib/mingw/tools/install/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/libgomp.dll.a
A:/ReifenScanner/extract_frames.exe: C:/ProgramData/chocolatey/lib/mingw/tools/install/mingw64/x86_64-w64-mingw32/lib/libmingwthrd.a
A:/ReifenScanner/extract_frames.exe: A:/OpenCV-MinGW-Build-OpenCV-4.5.5-x64/x64/mingw/lib/libopencv_imgcodecs455.dll.a
A:/ReifenScanner/extract_frames.exe: A:/OpenCV-MinGW-Build-OpenCV-4.5.5-x64/x64/mingw/lib/libopencv_dnn455.dll.a
A:/ReifenScanner/extract_frames.exe: A:/OpenCV-MinGW-Build-OpenCV-4.5.5-x64/x64/mingw/lib/libopencv_calib3d455.dll.a
A:/ReifenScanner/extract_frames.exe: A:/OpenCV-MinGW-Build-OpenCV-4.5.5-x64/x64/mingw/lib/libopencv_features2d455.dll.a
A:/ReifenScanner/extract_frames.exe: A:/OpenCV-MinGW-Build-OpenCV-4.5.5-x64/x64/mingw/lib/libopencv_flann455.dll.a
A:/ReifenScanner/extract_frames.exe: A:/OpenCV-MinGW-Build-OpenCV-4.5.5-x64/x64/mingw/lib/libopencv_imgproc455.dll.a
A:/ReifenScanner/extract_frames.exe: A:/OpenCV-MinGW-Build-OpenCV-4.5.5-x64/x64/mingw/lib/libopencv_core455.dll.a
A:/ReifenScanner/extract_frames.exe: CMakeFiles/extract_frames.dir/linkLibs.rsp
A:/ReifenScanner/extract_frames.exe: CMakeFiles/extract_frames.dir/objects1.rsp
A:/ReifenScanner/extract_frames.exe: CMakeFiles/extract_frames.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=A:\ReifenScanner\TireStitcher\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Linking CXX executable A:\ReifenScanner\extract_frames.exe"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\extract_frames.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/extract_frames.dir/build: A:/ReifenScanner/extract_frames.exe
.PHONY : CMakeFiles/extract_frames.dir/build

CMakeFiles/extract_frames.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\extract_frames.dir\cmake_clean.cmake
.PHONY : CMakeFiles/extract_frames.dir/clean

CMakeFiles/extract_frames.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" A:\ReifenScanner\TireStitcher A:\ReifenScanner\TireStitcher A:\ReifenScanner\TireStitcher\build A:\ReifenScanner\TireStitcher\build A:\ReifenScanner\TireStitcher\build\CMakeFiles\extract_frames.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/extract_frames.dir/depend

