
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "A:/ReifenScanner/TireStitcher/main.cpp" "CMakeFiles/stitch_tire.dir/main.cpp.obj" "gcc" "CMakeFiles/stitch_tire.dir/main.cpp.obj.d"
  "A:/ReifenScanner/TireStitcher/stitching/frame_extraction_utils.cpp" "CMakeFiles/stitch_tire.dir/stitching/frame_extraction_utils.cpp.obj" "gcc" "CMakeFiles/stitch_tire.dir/stitching/frame_extraction_utils.cpp.obj.d"
  "A:/ReifenScanner/TireStitcher/stitching/fs_util.cpp" "CMakeFiles/stitch_tire.dir/stitching/fs_util.cpp.obj" "gcc" "CMakeFiles/stitch_tire.dir/stitching/fs_util.cpp.obj.d"
  "A:/ReifenScanner/TireStitcher/stitching/image_blending.cpp" "CMakeFiles/stitch_tire.dir/stitching/image_blending.cpp.obj" "gcc" "CMakeFiles/stitch_tire.dir/stitching/image_blending.cpp.obj.d"
  "A:/ReifenScanner/TireStitcher/stitching/image_loader.cpp" "CMakeFiles/stitch_tire.dir/stitching/image_loader.cpp.obj" "gcc" "CMakeFiles/stitch_tire.dir/stitching/image_loader.cpp.obj.d"
  "A:/ReifenScanner/TireStitcher/stitching/movement_detection.cpp" "CMakeFiles/stitch_tire.dir/stitching/movement_detection.cpp.obj" "gcc" "CMakeFiles/stitch_tire.dir/stitching/movement_detection.cpp.obj.d"
  "A:/ReifenScanner/TireStitcher/stitching/panorama_processor.cpp" "CMakeFiles/stitch_tire.dir/stitching/panorama_processor.cpp.obj" "gcc" "CMakeFiles/stitch_tire.dir/stitching/panorama_processor.cpp.obj.d"
  "A:/ReifenScanner/TireStitcher/stitching/strip_extraction.cpp" "CMakeFiles/stitch_tire.dir/stitching/strip_extraction.cpp.obj" "gcc" "CMakeFiles/stitch_tire.dir/stitching/strip_extraction.cpp.obj.d"
  "A:/ReifenScanner/TireStitcher/stitching/utils.cpp" "CMakeFiles/stitch_tire.dir/stitching/utils.cpp.obj" "gcc" "CMakeFiles/stitch_tire.dir/stitching/utils.cpp.obj.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
