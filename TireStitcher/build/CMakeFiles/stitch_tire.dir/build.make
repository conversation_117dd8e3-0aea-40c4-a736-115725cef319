# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = A:\ReifenScanner\TireStitcher

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = A:\ReifenScanner\TireStitcher\build

# Include any dependencies generated for this target.
include CMakeFiles/stitch_tire.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/stitch_tire.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/stitch_tire.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/stitch_tire.dir/flags.make

CMakeFiles/stitch_tire.dir/codegen:
.PHONY : CMakeFiles/stitch_tire.dir/codegen

CMakeFiles/stitch_tire.dir/main.cpp.obj: CMakeFiles/stitch_tire.dir/flags.make
CMakeFiles/stitch_tire.dir/main.cpp.obj: CMakeFiles/stitch_tire.dir/includes_CXX.rsp
CMakeFiles/stitch_tire.dir/main.cpp.obj: A:/ReifenScanner/TireStitcher/main.cpp
CMakeFiles/stitch_tire.dir/main.cpp.obj: CMakeFiles/stitch_tire.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=A:\ReifenScanner\TireStitcher\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/stitch_tire.dir/main.cpp.obj"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/stitch_tire.dir/main.cpp.obj -MF CMakeFiles\stitch_tire.dir\main.cpp.obj.d -o CMakeFiles\stitch_tire.dir\main.cpp.obj -c A:\ReifenScanner\TireStitcher\main.cpp

CMakeFiles/stitch_tire.dir/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/stitch_tire.dir/main.cpp.i"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E A:\ReifenScanner\TireStitcher\main.cpp > CMakeFiles\stitch_tire.dir\main.cpp.i

CMakeFiles/stitch_tire.dir/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/stitch_tire.dir/main.cpp.s"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S A:\ReifenScanner\TireStitcher\main.cpp -o CMakeFiles\stitch_tire.dir\main.cpp.s

CMakeFiles/stitch_tire.dir/stitching/utils.cpp.obj: CMakeFiles/stitch_tire.dir/flags.make
CMakeFiles/stitch_tire.dir/stitching/utils.cpp.obj: CMakeFiles/stitch_tire.dir/includes_CXX.rsp
CMakeFiles/stitch_tire.dir/stitching/utils.cpp.obj: A:/ReifenScanner/TireStitcher/stitching/utils.cpp
CMakeFiles/stitch_tire.dir/stitching/utils.cpp.obj: CMakeFiles/stitch_tire.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=A:\ReifenScanner\TireStitcher\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/stitch_tire.dir/stitching/utils.cpp.obj"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/stitch_tire.dir/stitching/utils.cpp.obj -MF CMakeFiles\stitch_tire.dir\stitching\utils.cpp.obj.d -o CMakeFiles\stitch_tire.dir\stitching\utils.cpp.obj -c A:\ReifenScanner\TireStitcher\stitching\utils.cpp

CMakeFiles/stitch_tire.dir/stitching/utils.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/stitch_tire.dir/stitching/utils.cpp.i"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E A:\ReifenScanner\TireStitcher\stitching\utils.cpp > CMakeFiles\stitch_tire.dir\stitching\utils.cpp.i

CMakeFiles/stitch_tire.dir/stitching/utils.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/stitch_tire.dir/stitching/utils.cpp.s"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S A:\ReifenScanner\TireStitcher\stitching\utils.cpp -o CMakeFiles\stitch_tire.dir\stitching\utils.cpp.s

CMakeFiles/stitch_tire.dir/stitching/image_loader.cpp.obj: CMakeFiles/stitch_tire.dir/flags.make
CMakeFiles/stitch_tire.dir/stitching/image_loader.cpp.obj: CMakeFiles/stitch_tire.dir/includes_CXX.rsp
CMakeFiles/stitch_tire.dir/stitching/image_loader.cpp.obj: A:/ReifenScanner/TireStitcher/stitching/image_loader.cpp
CMakeFiles/stitch_tire.dir/stitching/image_loader.cpp.obj: CMakeFiles/stitch_tire.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=A:\ReifenScanner\TireStitcher\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/stitch_tire.dir/stitching/image_loader.cpp.obj"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/stitch_tire.dir/stitching/image_loader.cpp.obj -MF CMakeFiles\stitch_tire.dir\stitching\image_loader.cpp.obj.d -o CMakeFiles\stitch_tire.dir\stitching\image_loader.cpp.obj -c A:\ReifenScanner\TireStitcher\stitching\image_loader.cpp

CMakeFiles/stitch_tire.dir/stitching/image_loader.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/stitch_tire.dir/stitching/image_loader.cpp.i"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E A:\ReifenScanner\TireStitcher\stitching\image_loader.cpp > CMakeFiles\stitch_tire.dir\stitching\image_loader.cpp.i

CMakeFiles/stitch_tire.dir/stitching/image_loader.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/stitch_tire.dir/stitching/image_loader.cpp.s"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S A:\ReifenScanner\TireStitcher\stitching\image_loader.cpp -o CMakeFiles\stitch_tire.dir\stitching\image_loader.cpp.s

CMakeFiles/stitch_tire.dir/stitching/strip_extraction.cpp.obj: CMakeFiles/stitch_tire.dir/flags.make
CMakeFiles/stitch_tire.dir/stitching/strip_extraction.cpp.obj: CMakeFiles/stitch_tire.dir/includes_CXX.rsp
CMakeFiles/stitch_tire.dir/stitching/strip_extraction.cpp.obj: A:/ReifenScanner/TireStitcher/stitching/strip_extraction.cpp
CMakeFiles/stitch_tire.dir/stitching/strip_extraction.cpp.obj: CMakeFiles/stitch_tire.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=A:\ReifenScanner\TireStitcher\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/stitch_tire.dir/stitching/strip_extraction.cpp.obj"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/stitch_tire.dir/stitching/strip_extraction.cpp.obj -MF CMakeFiles\stitch_tire.dir\stitching\strip_extraction.cpp.obj.d -o CMakeFiles\stitch_tire.dir\stitching\strip_extraction.cpp.obj -c A:\ReifenScanner\TireStitcher\stitching\strip_extraction.cpp

CMakeFiles/stitch_tire.dir/stitching/strip_extraction.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/stitch_tire.dir/stitching/strip_extraction.cpp.i"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E A:\ReifenScanner\TireStitcher\stitching\strip_extraction.cpp > CMakeFiles\stitch_tire.dir\stitching\strip_extraction.cpp.i

CMakeFiles/stitch_tire.dir/stitching/strip_extraction.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/stitch_tire.dir/stitching/strip_extraction.cpp.s"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S A:\ReifenScanner\TireStitcher\stitching\strip_extraction.cpp -o CMakeFiles\stitch_tire.dir\stitching\strip_extraction.cpp.s

CMakeFiles/stitch_tire.dir/stitching/movement_detection.cpp.obj: CMakeFiles/stitch_tire.dir/flags.make
CMakeFiles/stitch_tire.dir/stitching/movement_detection.cpp.obj: CMakeFiles/stitch_tire.dir/includes_CXX.rsp
CMakeFiles/stitch_tire.dir/stitching/movement_detection.cpp.obj: A:/ReifenScanner/TireStitcher/stitching/movement_detection.cpp
CMakeFiles/stitch_tire.dir/stitching/movement_detection.cpp.obj: CMakeFiles/stitch_tire.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=A:\ReifenScanner\TireStitcher\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/stitch_tire.dir/stitching/movement_detection.cpp.obj"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/stitch_tire.dir/stitching/movement_detection.cpp.obj -MF CMakeFiles\stitch_tire.dir\stitching\movement_detection.cpp.obj.d -o CMakeFiles\stitch_tire.dir\stitching\movement_detection.cpp.obj -c A:\ReifenScanner\TireStitcher\stitching\movement_detection.cpp

CMakeFiles/stitch_tire.dir/stitching/movement_detection.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/stitch_tire.dir/stitching/movement_detection.cpp.i"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E A:\ReifenScanner\TireStitcher\stitching\movement_detection.cpp > CMakeFiles\stitch_tire.dir\stitching\movement_detection.cpp.i

CMakeFiles/stitch_tire.dir/stitching/movement_detection.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/stitch_tire.dir/stitching/movement_detection.cpp.s"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S A:\ReifenScanner\TireStitcher\stitching\movement_detection.cpp -o CMakeFiles\stitch_tire.dir\stitching\movement_detection.cpp.s

CMakeFiles/stitch_tire.dir/stitching/image_blending.cpp.obj: CMakeFiles/stitch_tire.dir/flags.make
CMakeFiles/stitch_tire.dir/stitching/image_blending.cpp.obj: CMakeFiles/stitch_tire.dir/includes_CXX.rsp
CMakeFiles/stitch_tire.dir/stitching/image_blending.cpp.obj: A:/ReifenScanner/TireStitcher/stitching/image_blending.cpp
CMakeFiles/stitch_tire.dir/stitching/image_blending.cpp.obj: CMakeFiles/stitch_tire.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=A:\ReifenScanner\TireStitcher\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/stitch_tire.dir/stitching/image_blending.cpp.obj"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/stitch_tire.dir/stitching/image_blending.cpp.obj -MF CMakeFiles\stitch_tire.dir\stitching\image_blending.cpp.obj.d -o CMakeFiles\stitch_tire.dir\stitching\image_blending.cpp.obj -c A:\ReifenScanner\TireStitcher\stitching\image_blending.cpp

CMakeFiles/stitch_tire.dir/stitching/image_blending.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/stitch_tire.dir/stitching/image_blending.cpp.i"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E A:\ReifenScanner\TireStitcher\stitching\image_blending.cpp > CMakeFiles\stitch_tire.dir\stitching\image_blending.cpp.i

CMakeFiles/stitch_tire.dir/stitching/image_blending.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/stitch_tire.dir/stitching/image_blending.cpp.s"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S A:\ReifenScanner\TireStitcher\stitching\image_blending.cpp -o CMakeFiles\stitch_tire.dir\stitching\image_blending.cpp.s

CMakeFiles/stitch_tire.dir/stitching/panorama_processor.cpp.obj: CMakeFiles/stitch_tire.dir/flags.make
CMakeFiles/stitch_tire.dir/stitching/panorama_processor.cpp.obj: CMakeFiles/stitch_tire.dir/includes_CXX.rsp
CMakeFiles/stitch_tire.dir/stitching/panorama_processor.cpp.obj: A:/ReifenScanner/TireStitcher/stitching/panorama_processor.cpp
CMakeFiles/stitch_tire.dir/stitching/panorama_processor.cpp.obj: CMakeFiles/stitch_tire.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=A:\ReifenScanner\TireStitcher\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object CMakeFiles/stitch_tire.dir/stitching/panorama_processor.cpp.obj"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/stitch_tire.dir/stitching/panorama_processor.cpp.obj -MF CMakeFiles\stitch_tire.dir\stitching\panorama_processor.cpp.obj.d -o CMakeFiles\stitch_tire.dir\stitching\panorama_processor.cpp.obj -c A:\ReifenScanner\TireStitcher\stitching\panorama_processor.cpp

CMakeFiles/stitch_tire.dir/stitching/panorama_processor.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/stitch_tire.dir/stitching/panorama_processor.cpp.i"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E A:\ReifenScanner\TireStitcher\stitching\panorama_processor.cpp > CMakeFiles\stitch_tire.dir\stitching\panorama_processor.cpp.i

CMakeFiles/stitch_tire.dir/stitching/panorama_processor.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/stitch_tire.dir/stitching/panorama_processor.cpp.s"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S A:\ReifenScanner\TireStitcher\stitching\panorama_processor.cpp -o CMakeFiles\stitch_tire.dir\stitching\panorama_processor.cpp.s

CMakeFiles/stitch_tire.dir/stitching/fs_util.cpp.obj: CMakeFiles/stitch_tire.dir/flags.make
CMakeFiles/stitch_tire.dir/stitching/fs_util.cpp.obj: CMakeFiles/stitch_tire.dir/includes_CXX.rsp
CMakeFiles/stitch_tire.dir/stitching/fs_util.cpp.obj: A:/ReifenScanner/TireStitcher/stitching/fs_util.cpp
CMakeFiles/stitch_tire.dir/stitching/fs_util.cpp.obj: CMakeFiles/stitch_tire.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=A:\ReifenScanner\TireStitcher\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object CMakeFiles/stitch_tire.dir/stitching/fs_util.cpp.obj"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/stitch_tire.dir/stitching/fs_util.cpp.obj -MF CMakeFiles\stitch_tire.dir\stitching\fs_util.cpp.obj.d -o CMakeFiles\stitch_tire.dir\stitching\fs_util.cpp.obj -c A:\ReifenScanner\TireStitcher\stitching\fs_util.cpp

CMakeFiles/stitch_tire.dir/stitching/fs_util.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/stitch_tire.dir/stitching/fs_util.cpp.i"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E A:\ReifenScanner\TireStitcher\stitching\fs_util.cpp > CMakeFiles\stitch_tire.dir\stitching\fs_util.cpp.i

CMakeFiles/stitch_tire.dir/stitching/fs_util.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/stitch_tire.dir/stitching/fs_util.cpp.s"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S A:\ReifenScanner\TireStitcher\stitching\fs_util.cpp -o CMakeFiles\stitch_tire.dir\stitching\fs_util.cpp.s

CMakeFiles/stitch_tire.dir/stitching/frame_extraction_utils.cpp.obj: CMakeFiles/stitch_tire.dir/flags.make
CMakeFiles/stitch_tire.dir/stitching/frame_extraction_utils.cpp.obj: CMakeFiles/stitch_tire.dir/includes_CXX.rsp
CMakeFiles/stitch_tire.dir/stitching/frame_extraction_utils.cpp.obj: A:/ReifenScanner/TireStitcher/stitching/frame_extraction_utils.cpp
CMakeFiles/stitch_tire.dir/stitching/frame_extraction_utils.cpp.obj: CMakeFiles/stitch_tire.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=A:\ReifenScanner\TireStitcher\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object CMakeFiles/stitch_tire.dir/stitching/frame_extraction_utils.cpp.obj"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/stitch_tire.dir/stitching/frame_extraction_utils.cpp.obj -MF CMakeFiles\stitch_tire.dir\stitching\frame_extraction_utils.cpp.obj.d -o CMakeFiles\stitch_tire.dir\stitching\frame_extraction_utils.cpp.obj -c A:\ReifenScanner\TireStitcher\stitching\frame_extraction_utils.cpp

CMakeFiles/stitch_tire.dir/stitching/frame_extraction_utils.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/stitch_tire.dir/stitching/frame_extraction_utils.cpp.i"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E A:\ReifenScanner\TireStitcher\stitching\frame_extraction_utils.cpp > CMakeFiles\stitch_tire.dir\stitching\frame_extraction_utils.cpp.i

CMakeFiles/stitch_tire.dir/stitching/frame_extraction_utils.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/stitch_tire.dir/stitching/frame_extraction_utils.cpp.s"
	g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S A:\ReifenScanner\TireStitcher\stitching\frame_extraction_utils.cpp -o CMakeFiles\stitch_tire.dir\stitching\frame_extraction_utils.cpp.s

# Object files for target stitch_tire
stitch_tire_OBJECTS = \
"CMakeFiles/stitch_tire.dir/main.cpp.obj" \
"CMakeFiles/stitch_tire.dir/stitching/utils.cpp.obj" \
"CMakeFiles/stitch_tire.dir/stitching/image_loader.cpp.obj" \
"CMakeFiles/stitch_tire.dir/stitching/strip_extraction.cpp.obj" \
"CMakeFiles/stitch_tire.dir/stitching/movement_detection.cpp.obj" \
"CMakeFiles/stitch_tire.dir/stitching/image_blending.cpp.obj" \
"CMakeFiles/stitch_tire.dir/stitching/panorama_processor.cpp.obj" \
"CMakeFiles/stitch_tire.dir/stitching/fs_util.cpp.obj" \
"CMakeFiles/stitch_tire.dir/stitching/frame_extraction_utils.cpp.obj"

# External object files for target stitch_tire
stitch_tire_EXTERNAL_OBJECTS =

A:/ReifenScanner/stitch_tire.exe: CMakeFiles/stitch_tire.dir/main.cpp.obj
A:/ReifenScanner/stitch_tire.exe: CMakeFiles/stitch_tire.dir/stitching/utils.cpp.obj
A:/ReifenScanner/stitch_tire.exe: CMakeFiles/stitch_tire.dir/stitching/image_loader.cpp.obj
A:/ReifenScanner/stitch_tire.exe: CMakeFiles/stitch_tire.dir/stitching/strip_extraction.cpp.obj
A:/ReifenScanner/stitch_tire.exe: CMakeFiles/stitch_tire.dir/stitching/movement_detection.cpp.obj
A:/ReifenScanner/stitch_tire.exe: CMakeFiles/stitch_tire.dir/stitching/image_blending.cpp.obj
A:/ReifenScanner/stitch_tire.exe: CMakeFiles/stitch_tire.dir/stitching/panorama_processor.cpp.obj
A:/ReifenScanner/stitch_tire.exe: CMakeFiles/stitch_tire.dir/stitching/fs_util.cpp.obj
A:/ReifenScanner/stitch_tire.exe: CMakeFiles/stitch_tire.dir/stitching/frame_extraction_utils.cpp.obj
A:/ReifenScanner/stitch_tire.exe: CMakeFiles/stitch_tire.dir/build.make
A:/ReifenScanner/stitch_tire.exe: A:/OpenCV-MinGW-Build-OpenCV-4.5.5-x64/x64/mingw/lib/libopencv_gapi455.dll.a
A:/ReifenScanner/stitch_tire.exe: A:/OpenCV-MinGW-Build-OpenCV-4.5.5-x64/x64/mingw/lib/libopencv_highgui455.dll.a
A:/ReifenScanner/stitch_tire.exe: A:/OpenCV-MinGW-Build-OpenCV-4.5.5-x64/x64/mingw/lib/libopencv_ml455.dll.a
A:/ReifenScanner/stitch_tire.exe: A:/OpenCV-MinGW-Build-OpenCV-4.5.5-x64/x64/mingw/lib/libopencv_objdetect455.dll.a
A:/ReifenScanner/stitch_tire.exe: A:/OpenCV-MinGW-Build-OpenCV-4.5.5-x64/x64/mingw/lib/libopencv_photo455.dll.a
A:/ReifenScanner/stitch_tire.exe: A:/OpenCV-MinGW-Build-OpenCV-4.5.5-x64/x64/mingw/lib/libopencv_stitching455.dll.a
A:/ReifenScanner/stitch_tire.exe: A:/OpenCV-MinGW-Build-OpenCV-4.5.5-x64/x64/mingw/lib/libopencv_video455.dll.a
A:/ReifenScanner/stitch_tire.exe: A:/OpenCV-MinGW-Build-OpenCV-4.5.5-x64/x64/mingw/lib/libopencv_videoio455.dll.a
A:/ReifenScanner/stitch_tire.exe: C:/ProgramData/chocolatey/lib/mingw/tools/install/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/libgomp.dll.a
A:/ReifenScanner/stitch_tire.exe: C:/ProgramData/chocolatey/lib/mingw/tools/install/mingw64/x86_64-w64-mingw32/lib/libmingwthrd.a
A:/ReifenScanner/stitch_tire.exe: A:/OpenCV-MinGW-Build-OpenCV-4.5.5-x64/x64/mingw/lib/libopencv_imgcodecs455.dll.a
A:/ReifenScanner/stitch_tire.exe: A:/OpenCV-MinGW-Build-OpenCV-4.5.5-x64/x64/mingw/lib/libopencv_dnn455.dll.a
A:/ReifenScanner/stitch_tire.exe: A:/OpenCV-MinGW-Build-OpenCV-4.5.5-x64/x64/mingw/lib/libopencv_calib3d455.dll.a
A:/ReifenScanner/stitch_tire.exe: A:/OpenCV-MinGW-Build-OpenCV-4.5.5-x64/x64/mingw/lib/libopencv_features2d455.dll.a
A:/ReifenScanner/stitch_tire.exe: A:/OpenCV-MinGW-Build-OpenCV-4.5.5-x64/x64/mingw/lib/libopencv_flann455.dll.a
A:/ReifenScanner/stitch_tire.exe: A:/OpenCV-MinGW-Build-OpenCV-4.5.5-x64/x64/mingw/lib/libopencv_imgproc455.dll.a
A:/ReifenScanner/stitch_tire.exe: A:/OpenCV-MinGW-Build-OpenCV-4.5.5-x64/x64/mingw/lib/libopencv_core455.dll.a
A:/ReifenScanner/stitch_tire.exe: CMakeFiles/stitch_tire.dir/linkLibs.rsp
A:/ReifenScanner/stitch_tire.exe: CMakeFiles/stitch_tire.dir/objects1.rsp
A:/ReifenScanner/stitch_tire.exe: CMakeFiles/stitch_tire.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=A:\ReifenScanner\TireStitcher\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Linking CXX executable A:\ReifenScanner\stitch_tire.exe"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\stitch_tire.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/stitch_tire.dir/build: A:/ReifenScanner/stitch_tire.exe
.PHONY : CMakeFiles/stitch_tire.dir/build

CMakeFiles/stitch_tire.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\stitch_tire.dir\cmake_clean.cmake
.PHONY : CMakeFiles/stitch_tire.dir/clean

CMakeFiles/stitch_tire.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" A:\ReifenScanner\TireStitcher A:\ReifenScanner\TireStitcher A:\ReifenScanner\TireStitcher\build A:\ReifenScanner\TireStitcher\build A:\ReifenScanner\TireStitcher\build\CMakeFiles\stitch_tire.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/stitch_tire.dir/depend

